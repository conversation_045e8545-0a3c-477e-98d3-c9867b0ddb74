package main

import (
	"os"
	"strconv"
	"time"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/log"
)

// pickCpPPO 使用PPO算法选择最优协议
func (server *server) pickCpPPO() {
	// 初始化PPO强化学习代理
	server.ppoAgent = NewPPOAgent()

	// 尝试加载模型（如果存在）
	modelFile := "ppo_model.json"
	if modelPath := os.Getenv("PPO_MODEL_PATH"); modelPath != "" {
		modelFile = modelPath
	}
	if _, err := os.Stat(modelFile); err == nil {
		log.Infof("尝试加载PPO模型: %s", modelFile)
		err := server.ppoAgent.LoadModel(modelFile)
		if err != nil {
			log.Warningf("加载PPO模型失败: %v, 将使用新模型", err)
		} else {
			// 如果加载成功，设置当前协议为模型中的lastAction
			if server.ppoAgent.lastAction != "" {
				server.mcp.SetCurrCP(server.ppoAgent.lastAction)
				log.Infof("从加载的PPO模型恢复协议为: %s", server.ppoAgent.lastAction)
				// 需要将加载的lastAction设为currentProtocol，以便第一次计算奖励时认为action未改变
				server.ppoAgent.currentProtocol = server.ppoAgent.lastAction
			} else {
				initialProtocol := server.ppoAgent.currentProtocol
				server.mcp.SetCurrCP(initialProtocol)
				log.Infof("PPO模型已加载，但无上次动作，初始协议设置为: %s", initialProtocol)
			}
		}
	} else {
		// 设置默认初始协议
		initialProtocol := server.ppoAgent.currentProtocol
		server.mcp.SetCurrCP(initialProtocol)
		log.Infof("未找到PPO模型文件，初始协议设置为: %s", initialProtocol)
	}

	// 设置协议切换间隔
	switchInterval := 60 * time.Second
	if interval, err := strconv.Atoi(os.Getenv("PROTOCOL_SWITCH_INTERVAL_SECONDS")); err == nil && interval > 0 {
		switchInterval = time.Duration(interval) * time.Second
	}

	server.switchTicker = time.NewTicker(switchInterval)
	log.Infof("PPO协议切换检查间隔: %v", switchInterval)

	// 启动协议切换协程
	go func() {
		var prevState State // 存储上一个时间窗口的状态
		firstRun := true

		for range server.switchTicker.C {
			// 1. 获取当前状态
			currentState := server.ppoAgent.getCurrentState()

			// 2. 如果不是第一次运行，计算奖励并更新PPO模型
			if !firstRun {
				// 动作是否改变取决于上个周期选择的动作(lastAction)和本周期开始时的协议(currentProtocol)
				actionChanged := server.ppoAgent.lastAction != server.ppoAgent.currentProtocol
				reward := server.ppoAgent.calculateReward(prevState, currentState, actionChanged)

				// 更新PPO模型
				server.ppoAgent.update(prevState, server.ppoAgent.lastAction, reward, currentState)
				log.Infof("PPO周期完成: PrevState: {LAT:%.2f, RW:%.2f}, Action: %s, Reward: %.2f -> CurrState: {LAT:%.2f, RW:%.2f}",
					prevState.AverageLatency, prevState.ReadWriteRatio, server.ppoAgent.lastAction, reward,
					currentState.AverageLatency, currentState.ReadWriteRatio)
			}

			// 3. 为下一个周期选择动作 (协议)
			nextAction := server.ppoAgent.selectNextProtocol(currentState)

			// 4. 更新PPO Agent的内部状态以备下一个周期使用
			server.ppoAgent.lastState = currentState                 // 当前状态成为下一个周期的lastState
			server.ppoAgent.lastAction = nextAction                  // 即将执行的动作成为下一个周期的lastAction
			server.ppoAgent.currentProtocol = server.mcp.GetCurrCP() // 记录本周期开始时的协议

			// 5. 应用选择的协议 (如果与当前不同)
			if nextAction != server.ppoAgent.currentProtocol {
				server.mcp.SetCurrCP(nextAction)
				server.ppoAgent.lastSwitchTime = time.Now() // 记录切换时间
				log.Infof("PPO Agent切换协议: %s -> %s", server.ppoAgent.currentProtocol, nextAction)
			}

			// 6. 重置统计数据并更新prevState
			prevState = currentState // 当前状态成为下一个周期的prevState
			server.ppoAgent.resetStats()
			firstRun = false

			// 定期保存模型
			if err := server.ppoAgent.SaveModel(modelFile); err != nil {
				log.Warningf("保存PPO模型失败: %v", err)
			}
		}
	}()
}

// RecordRequestPPO 记录请求性能指标
func (server *server) RecordRequestPPO(cmd paxi.Command, latency time.Duration) {
	if server.ppoAgent != nil {
		server.ppoAgent.RecordRequest(cmd, latency)
	}
}
