package main

import (
	"encoding/gob"
	"encoding/json"
	"flag"
	"fmt"
	"hash/fnv"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/signal"
	"reflect"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/epaxos"
	"github.com/ailidani/paxi/experiments"
	"github.com/ailidani/paxi/log"
	"github.com/ailidani/paxi/m2paxos"
	"github.com/ailidani/paxi/mcp"
	"github.com/ailidani/paxi/paxos"
	"github.com/ailidani/paxi/vpaxos"
	cmap "github.com/orcaman/concurrent-map/v2"
)

var id = flag.String("id", "", "ID in format of Zone.Node.")
var PendingRequests = cmap.New[*paxi.Request]()

// http request header names
const (
	HTTPClientID  = "Id"
	HTTPCommandID = "Cid"
	HTTPTimestamp = "Timestamp"
	HTTPNodeID    = "Id"
)

type server struct {
	msgQueue     chan interface{} // 消息队列，不包含客户端请求
	sender       paxi.Socket      // 发送消息
	stateMachine paxi.Database    // kv状态机
	mcp          *mcp.Mcp         // 元一致性协议
	paxos        *paxos.Replica   // Paxos协议
	epaxos       *epaxos.Replica  // epaxos
	m2paxos      *m2paxos.Replica // flexible-paxos
	vpaxos       *vpaxos.Replica  // vpaxos
	// obj1         atomic.Int64
	// mu           sync.Mutex
	server *http.Server
	// rlAgent      *RLAgent      // Q-learning强化学习代理
	ppoAgent     *PPOAgent    // PPO强化学习代理（新增）
	switchTicker *time.Ticker // 协议切换定时器
}

func (s *server) run() error {
	s.msgQueue = make(chan interface{}, 1024)
	s.mcp = mcp.NewMcp()
	// 初始化各个协议的replica
	s.sender = paxi.NewSocket(paxi.ID(*id), paxi.GetConfig().Addrs)

	//初始化server
	s.stateMachine = paxi.NewDatabase()

	// 先初始化各个协议实例，让它们注册自己的消息类型
	s.paxos = paxos.NewReplica(paxi.ID(*id), s.sender)
	s.epaxos = epaxos.NewReplica(paxi.ID(*id), s.sender)
	s.m2paxos = m2paxos.NewReplica(paxi.ID(*id), s.sender)
	s.vpaxos = vpaxos.NewReplica(paxi.ID(*id), s.sender)

	// 最后注册基础消息类型，这样它们会覆盖之前的注册
	gob.Register(paxi.Request{})
	gob.Register(paxi.Reply{})
	gob.Register(paxi.Command{})
	gob.Register(paxi.Value{})

	//启动server
	go s.loop()
	//启动http server
	go s.http()
	go s.pickCp()
	// 启动各个协议的replica
	s.paxos.Run()
	s.epaxos.Run()
	s.m2paxos.Run()
	s.vpaxos.Run()

	// 消息分发
	go func() {
		log.Infof("Start dispatch message")
		for m := range s.msgQueue {
			msgType := reflect.TypeOf(m)
			//log msg and type
			log.Infof("msg: %v, type: %v", m, msgType)
			// 逐个转发给各个协议，某个协议会拦截它需要的消息包，如果所有协议都没有拦截，说明这个消息包不是整个系统的
			if s.paxos.Ask(m) {
				continue
			}
			if s.vpaxos.Ask(m) {
				continue
			}
			if s.epaxos.Ask(m) {
				continue
			}
			if s.m2paxos.Ask(m) {
				continue
			}

			// TODO 其它协议
			log.Warningf("Unexcepted message type %v\n", msgType)
		}
	}()

	return nil
}

func (s *server) loop() {
	config := paxi.GetConfig()
	addr := config.Addrs[paxi.ID(*id)]
	//remove http://
	addr = strings.TrimPrefix(addr, "http://")
	log.Infof("Listen on %s", addr)
	listener, err := net.Listen("tcp", addr)
	if err != nil {
		log.Fatal(err)
	}
	defer listener.Close()

	for {
		// 等待连接
		conn, err := listener.Accept()
		if err != nil {
			log.Warningf("TCP Accept error: %v\n", err)
			continue
		}
		remoteAddr := conn.RemoteAddr()
		log.Infof("Accept connect, <remote_address=%v>\n", remoteAddr)

		// 新开一个协程处理消息
		go func(conn net.Conn) {
			defer conn.Close()
			decoder := gob.NewDecoder(conn)
			encoder := gob.NewEncoder(conn)
			for {
				var m interface{}
				err := decoder.Decode(&m)
				if err != nil {
					// 说明这个连接断开了
					log.Infof("Socket disconnected, <remote_addr=%v, err=%v>\n", conn.RemoteAddr(), err)
					break
				}
				log.Infof("m: %v", m)
				// 处理消息
				msgType := reflect.TypeOf(m)
				log.Infof("msgType: %v", msgType)
				if msgType == reflect.TypeOf(paxi.Request{}) {
					// 客户端请求
					msg := m.(paxi.Request)
					// 转发请求
					log.Infof("Forward request %v to %v", msg, msg.NodeID)
					// go func() { s.sender.Send(msg.NodeID, <-msg.C) }()
					go s.handleClientRequest(&msg, encoder)
					continue
				} else if msgType == reflect.TypeOf(paxi.Reply{}) {
					reply := m.(paxi.Reply)
					req, ok := PendingRequests.Get(msg2hash(reply.Command))
					if !ok {
						log.Warningf("Fail to load request %v %v %v\n", reply, ok, req)
						continue
					}
					req.C <- reply
				} else {
					// 协议消息
					s.msgQueue <- m
				}
			}
		}(conn)
	}
}

// 切换
func (server *server) pickCp() {
	// 使用PPO算法选择最优共识协议
	// 直接调用PPO实现
	server.pickCpPPO()
}

func msg2hash(cmd paxi.Command) string {
	// 计算消息的hash值
	h := fnv.New64()
	data, _ := json.Marshal(cmd)
	return string(h.Sum(data))
}

// handleClientRequest 处理客户端请求，阻塞直到协议达成一致返回给客户端
func (server *server) handleClientRequest(msg *paxi.Request, encoder *gob.Encoder) {
	// 设置默认超时时间（10秒），可通过环境变量配置
	timeoutSeconds := 10
	if to, err := strconv.Atoi(os.Getenv("CLIENT_REQUEST_TIMEOUT_SECONDS")); err == nil && to > 0 {
		timeoutSeconds = to
	}

	msg.C = make(chan paxi.Reply, 1)
	currCp := server.mcp.GetCurrCP()
	if msg.Command.McpSeqNo == 0 {
		server.mcp.AddSeqNo(msg)             // 添加序列号
		server.mcp.AddProcessor(msg, currCp) // 添加一致性协议标签
	}
	Latency := time.Now()
	log.Infof("Handle client request by using %s", currCp)
	PendingRequests.Set(msg2hash(msg.Command), msg)

	switch currCp {
	case "paxos":
		server.paxos.SendMessage(*msg)
	case "m2paxos":
		server.m2paxos.SendMessage(*msg)
	case "epaxos":
		server.epaxos.SendMessage(*msg)
	case "vpaxos":
		server.vpaxos.SendMessage(*msg)
	default:
		log.Warningf("Unknown protocal %s\n", currCp)
		// 因为已经为请求打了序列号标签，如果执行失败则必须终止程序
		panic("Unknown protocal " + currCp)
	}

	if encoder != nil {
		// 使用select和超时机制
		var reply paxi.Reply
		timeoutChan := time.After(time.Duration(timeoutSeconds) * time.Second)
		select {
		case reply = <-msg.C:
			// 成功获取响应
		case <-timeoutChan:
			// 超时处理
			log.Warningf("Request %v timed out after %d seconds", msg.Command, timeoutSeconds)
			reply = paxi.Reply{
				Command: msg.Command,
				Err:     fmt.Errorf("request timed out after %d seconds", timeoutSeconds),
			}
		}

		reply.Latency = time.Since(Latency)
		reply.Command.McpSeqNo = msg.Command.McpSeqNo
		log.Debugf("reply: %v", reply)
		close(msg.C)

		// 记录请求性能指标到PPO代理
		if server.ppoAgent != nil {
			server.ppoAgent.RecordRequest(reply.Command, reply.Latency)
		}

		err := encoder.Encode(&reply)
		if err != nil {
			// 回复客户端失败
			log.Warningf("Fail to reply client request %v\n", msg)
		}
	}
}

func (server *server) http() {
	mux := http.NewServeMux()
	mux.HandleFunc("/", server.handleRoot)
	mux.HandleFunc("/history", server.handleHistory)
	mux.HandleFunc("/crash", server.handleCrash)
	mux.HandleFunc("/drop", server.handleDrop)
	mux.HandleFunc("/slow", server.handleSlow)
	mux.HandleFunc("/rl/status", server.handleRLStatus)
	url, err := url.Parse(paxi.GetConfig().HTTPAddrs[paxi.ID(*id)])
	if err != nil {
		log.Fatal("http url parse error: ", err)
	}
	port := ":" + url.Port()

	// 设置超时配置
	readTimeout := 30 * time.Second
	writeTimeout := 30 * time.Second
	idleTimeout := 120 * time.Second

	// 从环境变量读取配置
	if t, err := strconv.Atoi(os.Getenv("HTTP_READ_TIMEOUT_SECONDS")); err == nil && t > 0 {
		readTimeout = time.Duration(t) * time.Second
	}
	if t, err := strconv.Atoi(os.Getenv("HTTP_WRITE_TIMEOUT_SECONDS")); err == nil && t > 0 {
		writeTimeout = time.Duration(t) * time.Second
	}
	if t, err := strconv.Atoi(os.Getenv("HTTP_IDLE_TIMEOUT_SECONDS")); err == nil && t > 0 {
		idleTimeout = time.Duration(t) * time.Second
	}

	server.server = &http.Server{
		Addr:           port,
		Handler:        mux,
		ReadTimeout:    readTimeout,
		WriteTimeout:   writeTimeout,
		IdleTimeout:    idleTimeout,
		MaxHeaderBytes: 1 << 20, // 1MB
	}

	log.Infof("http server starting on %s with timeouts: read=%v, write=%v, idle=%v",
		port, readTimeout, writeTimeout, idleTimeout)
	log.Fatal(server.server.ListenAndServe())
}

func (server *server) handleRoot(w http.ResponseWriter, r *http.Request) {
	var req paxi.Request
	var cmd paxi.Command
	var err error

	// get all http headers
	req.Properties = make(map[string]string)
	for k := range r.Header {
		if k == HTTPClientID {
			cmd.ClientID = paxi.ID(r.Header.Get(HTTPClientID))
			continue
		}
		if k == HTTPCommandID {
			cmd.CommandID, err = strconv.Atoi(r.Header.Get(HTTPCommandID))
			if err != nil {
				log.Error(err)
			}
			continue
		}
		req.Properties[k] = r.Header.Get(k)
	}

	// get command key and value
	if len(r.URL.Path) > 1 {
		i, err := strconv.Atoi(r.URL.Path[1:])
		if err != nil {
			http.Error(w, "invalid path", http.StatusBadRequest)
			log.Error(err)
			return
		}
		cmd.Key = paxi.Key(i)
		if r.Method == http.MethodPut || r.Method == http.MethodPost {
			body, err := ioutil.ReadAll(r.Body)
			if err != nil {
				log.Error("error reading body: ", err)
				http.Error(w, "cannot read body", http.StatusBadRequest)
				return
			}
			cmd.Value = paxi.Value(body)
		}
	} else {
		body, err := ioutil.ReadAll(r.Body)
		if err != nil {
			log.Error("error reading body: ", err)
			http.Error(w, "cannot read body", http.StatusBadRequest)
			return
		}
		json.Unmarshal(body, &cmd)
	}
	req.Command = cmd
	req.Command.McpSeqNo = 0
	req.Timestamp = time.Now().UnixNano()
	req.NodeID = paxi.ID(*id) // TODO does this work when forward twice
	// req.C = make(chan paxi.Reply, 1)
	log.Infof("handle http client request: %v", req)
	Latency := time.Now()
	server.handleClientRequest(&req, nil)
	// server.msgQueue <- req
	log.Infof("handle http client request done")
	reply := <-req.C
	reply.Latency = time.Since(Latency)
	reply.Command.McpSeqNo = req.Command.McpSeqNo
	log.Infof("http reply: %v", reply)
	close(req.C)

	// 记录HTTP请求性能指标到PPO代理
	if server.ppoAgent != nil {
		server.ppoAgent.RecordRequest(reply.Command, reply.Latency)
	}

	if reply.Err != nil {
		http.Error(w, reply.Err.Error(), http.StatusInternalServerError)
		return
	}

	// set all http headers
	w.Header().Set(HTTPClientID, string(reply.Command.ClientID))
	w.Header().Set(HTTPCommandID, strconv.Itoa(reply.Command.CommandID))
	for k, v := range reply.Properties {
		w.Header().Set(k, v)
	}

	_, err = io.WriteString(w, string(reply.Value))
	if err != nil {
		log.Error(err)
	}
}

func (server *server) handleHistory(w http.ResponseWriter, r *http.Request) {
	w.Header().Set(HTTPNodeID, string(paxi.ID(*id)))
	k, err := strconv.Atoi(r.URL.Query().Get("key"))
	if err != nil {
		log.Error(err)
		http.Error(w, "invalide key", http.StatusBadRequest)
		return
	}
	h := server.stateMachine.History(paxi.Key(k))
	b, _ := json.Marshal(h)
	_, err = w.Write(b)
	if err != nil {
		log.Error(err)
	}
}

func (server *server) handleCrash(w http.ResponseWriter, r *http.Request) {
	t, err := strconv.Atoi(r.URL.Query().Get("t"))
	if err != nil {
		log.Error(err)
		http.Error(w, "invalide time", http.StatusBadRequest)
		return
	}
	server.sender.Crash(t)
}

func (server *server) handleDrop(w http.ResponseWriter, r *http.Request) {
	id := r.URL.Query().Get("id")
	t, err := strconv.Atoi(r.URL.Query().Get("t"))
	if err != nil {
		log.Error(err)
		http.Error(w, "invalide time", http.StatusBadRequest)
		return
	}
	server.sender.Drop(paxi.ID(id), t)
}

func (server *server) handleSlow(w http.ResponseWriter, r *http.Request) {
	id := r.URL.Query().Get("id")
	t, err := strconv.Atoi(r.URL.Query().Get("t"))
	if err != nil {
		log.Error(err)
		http.Error(w, "invalid time duration", http.StatusBadRequest)
		return
	}

	l, err := strconv.Atoi(r.URL.Query().Get("delay"))
	if err != nil {
		log.Error(err)
		http.Error(w, "invalid delay value", http.StatusBadRequest)
		return
	}
	server.sender.Slow(paxi.ID(id), l, t)
}

func (server *server) handleRLStatus(w http.ResponseWriter, r *http.Request) {
	if server.ppoAgent == nil {
		http.Error(w, "PPO agent not initialized", http.StatusInternalServerError)
		return
	}

	// 获取当前状态
	state := server.ppoAgent.getCurrentState()

	// 创建状态报告
	type AgentStatus struct {
		CurrentProtocol       string    `json:"current_protocol"`
		LastAction            string    `json:"last_chosen_action"`
		LastSwitchTime        time.Time `json:"last_switch_time"`
		MonitoringWindowStart time.Time `json:"monitoring_window_start_time"`
		CurrentMetrics        State     `json:"current_metrics"` // 显示具体指标
		Algorithm             string    `json:"algorithm"`       // 标识使用的是PPO算法
	}

	server.ppoAgent.mu.RLock() // 加读锁保护访问agent内部状态
	status := AgentStatus{
		CurrentProtocol:       server.mcp.GetCurrCP(),
		LastAction:            server.ppoAgent.lastAction,
		LastSwitchTime:        server.ppoAgent.lastSwitchTime,
		MonitoringWindowStart: server.ppoAgent.lastResetTime,
		CurrentMetrics:        state,
		Algorithm:             "PPO", // 标识使用的算法
	}
	server.ppoAgent.mu.RUnlock()

	// 将状态转换为JSON并返回
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(status)
}

// 与现有server系统集成PPO实验功能
func (server *server) IntegrateExperimentFramework() {
	// 检查是否启用实验模式
	if os.Getenv("EXPERIMENT_MODE") != "true" {
		return
	}

	log.Infof("启用实验模式，集成实验框架")

	// 创建实验运行器
	experimentRunner := experiments.NewExperimentRunner()

	// 设置服务器集成
	serverIntegration := &ServerIntegrationImpl{
		server: server,
	}
	experimentRunner.SetServerIntegration(serverIntegration)

	// 在后台运行实验
	go func() {
		time.Sleep(10 * time.Second) // 等待服务器启动完成

		log.Infof("开始运行PPO性能评估实验")
		if err := experimentRunner.RunFullExperimentSuite(); err != nil {
			log.Errorf("实验套件运行失败: %v", err)
		} else {
			log.Infof("实验套件运行完成")
		}
	}()
}

// ServerIntegrationImpl 服务器集成实现
type ServerIntegrationImpl struct {
	server *server
}

// SetProtocol 设置协议
func (si *ServerIntegrationImpl) SetProtocol(protocol string) error {
	if si.server.mcp != nil {
		si.server.mcp.SetCurrCP(protocol)
		log.Infof("协议已设置为: %s", protocol)
		return nil
	}
	return fmt.Errorf("MCP未初始化")
}

// GetCurrentProtocol 获取当前协议
func (si *ServerIntegrationImpl) GetCurrentProtocol() string {
	if si.server.mcp != nil {
		return si.server.mcp.GetCurrCP()
	}
	return "unknown"
}

// EnablePPO 启用/禁用PPO
func (si *ServerIntegrationImpl) EnablePPO(enabled bool) error {
	if enabled {
		if si.server.ppoAgent == nil {
			si.server.ppoAgent = NewPPOAgent()
		}
		si.server.ppoAgent.trainingEnabled = true
		log.Infof("PPO已启用")
	} else {
		if si.server.ppoAgent != nil {
			si.server.ppoAgent.trainingEnabled = false
		}
		log.Infof("PPO已禁用")
	}
	return nil
}

// GetPPOAgent 获取PPO代理
func (si *ServerIntegrationImpl) GetPPOAgent() interface{} {
	return si.server.ppoAgent
}

// RecordRequest 记录请求
func (si *ServerIntegrationImpl) RecordRequest(cmd interface{}, latency time.Duration) error {
	if paxiCmd, ok := cmd.(paxi.Command); ok {
		if si.server.ppoAgent != nil {
			si.server.ppoAgent.RecordRequest(paxiCmd, latency)
		}
	}
	return nil
}

// GetCurrentMetrics 获取当前指标
func (si *ServerIntegrationImpl) GetCurrentMetrics() experiments.PerformanceMetrics {
	if si.server.ppoAgent != nil {
		state := si.server.ppoAgent.getCurrentState()
		return experiments.PerformanceMetrics{
			Timestamp:      time.Now(),
			Protocol:       si.GetCurrentProtocol(),
			RequestCount:   int64(si.server.ppoAgent.requestCounter),
			AverageLatency: state.AverageLatency,
			LatencyStdDev:  state.LatencyStdDev,
			Throughput:     state.Throughput,
			SuccessRate:    99.0, // 假设成功率
		}
	}

	// 返回默认指标
	return experiments.PerformanceMetrics{
		Timestamp: time.Now(),
		Protocol:  si.GetCurrentProtocol(),
	}
}

func main() {
	paxi.Init()
	paxi.SetCurrentNodeID(paxi.ID(*id))
	server := new(server)

	err := server.run()
	if err != nil {
		log.Fatal(err)
	}

	// 模型加载逻辑已移至 pickCp 初始化阶段

	//捕捉kill信号
	ch := make(chan os.Signal, 1)
	signal.Notify(ch, os.Interrupt, syscall.SIGTERM, syscall.SIGKILL, syscall.SIGINT, syscall.SIGQUIT, syscall.SIGABRT)
	<-ch

	// 保存PPO模型
	if server.ppoAgent != nil {
		modelFile := "ppo_model.json"
		if modelPath := os.Getenv("PPO_MODEL_PATH"); modelPath != "" {
			modelFile = modelPath
		}

		log.Infof("服务器关闭，保存PPO模型到: %s", modelFile)
		err := server.ppoAgent.SaveModel(modelFile)
		if err != nil {
			log.Warningf("保存PPO模型失败: %v", err)
		}
	}

	// server.stateMachine.Dump() // 原始的dump，如果需要可以保留
}
