package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"math/rand"
	"sync"
	"time"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/log"
)

// 协议类型
const (
	PAXOS   = "paxos"
	EPAXOS  = "epaxos"
	M2PAXOS = "m2paxos"
	VPAXOS  = "vpaxos"
)

// 状态特征
type State struct {
	RequestRate      float64 // 请求速率（每秒请求数）
	AverageLatency   float64 // 平均延迟（毫秒）
	LatencyStdDev    float64 // 延迟标准差（毫秒）
	Throughput       float64 // 吞吐量（每秒请求数）
	ReadWriteRatio   float64 // 读写比例
	KeyDistribution  float64 // 键分布的熵（衡量访问模式的集中度）
	NetworkCondition float64 // 网络状况（0-1，1表示最佳）
}

// 将State转换为特征向量
func (s State) ToFeatures() []float64 {
	return []float64{
		s.RequestRate,
		s.AverageLatency,
		s.LatencyStdDev,
		s.Throughput,
		s.ReadWriteRatio,
		s.KeyDistribution,
		s.NetworkCondition,
	}
}

// PPONetwork 表示PPO算法中的策略和价值网络
type PPONetwork struct {
	sync.RWMutex
	// 策略网络参数 (简化版本，实际应使用深度学习库)
	PolicyWeights [][]float64 // 输入特征 -> 动作概率
	// 价值网络参数
	ValueWeights []float64 // 输入特征 -> 状态价值

	// 存储过去的轨迹数据
	States        [][]float64
	Actions       []int
	ActionProbs   [][]float64
	Rewards       []float64
	NextStates    [][]float64
	ActionIndices map[string]int // 协议名称到索引的映射
	Protocols     []string       // 可用协议列表
}

// PPOAgent 强化学习代理
type PPOAgent struct {
	network        *PPONetwork
	learningRate   float64
	discountFactor float64
	clipEpsilon    float64 // PPO裁剪参数
	switchPenalty  float64 // 切换惩罚因子
	entropyCoeff   float64 // 熵正则化系数
	batchSize      int     // 每次更新的批次大小
	epochs         int     // 每批数据训练的轮数

	// 当前状态和监控
	mu              sync.RWMutex
	requestCounter  int
	latencySum      float64
	latencyValues   []float64
	readCounter     int
	writeCounter    int
	keyAccess       map[paxi.Key]int
	lastResetTime   time.Time
	lastSwitchTime  time.Time
	lastState       State
	lastAction      string
	currentProtocol string
	trainingEnabled bool // 是否启用训练
}

// 创建新的PPO代理
func NewPPOAgent() *PPOAgent {
	protocols := []string{PAXOS, EPAXOS, M2PAXOS, VPAXOS}
	actionIndices := make(map[string]int)
	for i, p := range protocols {
		actionIndices[p] = i
	}

	// 创建简单的线性网络（在实际应用中应使用深度学习库）
	network := &PPONetwork{
		PolicyWeights: make([][]float64, 7), // 7个输入特征
		ValueWeights:  make([]float64, 7),
		ActionIndices: actionIndices,
		Protocols:     protocols,
		States:        make([][]float64, 0),
		Actions:       make([]int, 0),
		ActionProbs:   make([][]float64, 0),
		Rewards:       make([]float64, 0),
		NextStates:    make([][]float64, 0),
	}

	// 初始化网络参数
	for i := range network.PolicyWeights {
		network.PolicyWeights[i] = make([]float64, len(protocols))
		for j := range network.PolicyWeights[i] {
			network.PolicyWeights[i][j] = rand.NormFloat64() * 0.1
		}
		network.ValueWeights[i] = rand.NormFloat64() * 0.1
	}

	agent := &PPOAgent{
		network:         network,
		learningRate:    0.001,
		discountFactor:  0.99,
		clipEpsilon:     0.2,
		switchPenalty:   0.5,
		entropyCoeff:    0.01,
		batchSize:       64,
		epochs:          4,
		keyAccess:       make(map[paxi.Key]int),
		latencyValues:   make([]float64, 0, 1000),
		lastResetTime:   time.Now(),
		lastSwitchTime:  time.Now(),
		currentProtocol: PAXOS, // 默认从Paxos开始
		trainingEnabled: true,  // 默认启用训练
	}

	return agent
}

// 记录请求
func (agent *PPOAgent) RecordRequest(cmd paxi.Command, latency time.Duration) {
	agent.mu.Lock()
	defer agent.mu.Unlock()

	latencyMs := float64(latency.Milliseconds())
	agent.requestCounter++
	agent.latencySum += latencyMs
	agent.latencyValues = append(agent.latencyValues, latencyMs)

	// 记录读写操作
	if len(cmd.Value) == 0 {
		agent.readCounter++
	} else {
		agent.writeCounter++
	}

	// 记录键访问
	agent.keyAccess[cmd.Key]++
}

// 计算标准差
func calculateStdDev(data []float64, mean float64) float64 {
	if len(data) < 2 {
		return 0.0
	}
	varianceSum := 0.0
	for _, val := range data {
		diff := val - mean
		varianceSum += diff * diff
	}
	variance := varianceSum / float64(len(data)-1)
	return math.Sqrt(variance)
}

// 计算熵
func calculateEntropy(probs []float64) float64 {
	entropy := 0.0
	for _, p := range probs {
		if p > 0 {
			entropy -= p * math.Log(p)
		}
	}
	return entropy
}

// 获取当前状态
func (agent *PPOAgent) getCurrentState() State {
	agent.mu.RLock()
	defer agent.mu.RUnlock()

	duration := time.Since(agent.lastResetTime).Seconds()
	if duration == 0 {
		duration = 1 // 避免除以零
	}

	// 计算请求速率和吞吐量
	requestRate := float64(agent.requestCounter) / duration
	throughput := requestRate

	// 计算平均延迟
	averageLatency := 0.0
	if agent.requestCounter > 0 {
		averageLatency = agent.latencySum / float64(agent.requestCounter)
	}

	// 计算延迟标准差
	latencyStdDev := calculateStdDev(agent.latencyValues, averageLatency)

	// 计算读写比例
	readWriteRatio := 1.0
	if agent.writeCounter > 0 {
		readWriteRatio = float64(agent.readCounter) / float64(agent.writeCounter)
	} else if agent.readCounter > 0 {
		readWriteRatio = 100.0 // 近似表示纯读场景
	}

	// 计算键分布的熵
	keyDistribution := 0.0
	if len(agent.keyAccess) > 0 {
		totalAccess := 0
		for _, count := range agent.keyAccess {
			totalAccess += count
		}

		entropy := 0.0
		if totalAccess > 0 {
			for _, count := range agent.keyAccess {
				p := float64(count) / float64(totalAccess)
				entropy -= p * math.Log(p)
			}
			// 归一化熵值到0-1
			maxEntropy := math.Log(float64(len(agent.keyAccess)))
			if maxEntropy > 0 {
				keyDistribution = entropy / maxEntropy
			}
		}
	}

	// 网络状况暂设为1.0（最佳）
	networkCondition := 1.0

	return State{
		RequestRate:      requestRate,
		AverageLatency:   averageLatency,
		LatencyStdDev:    latencyStdDev,
		Throughput:       throughput,
		ReadWriteRatio:   readWriteRatio,
		KeyDistribution:  keyDistribution,
		NetworkCondition: networkCondition,
	}
}

// 计算奖励
func (agent *PPOAgent) calculateReward(oldState, newState State, actionChanged bool) float64 {
	// 延迟改善奖励
	latencyImprovementReward := 0.0
	if oldState.AverageLatency > 0 {
		latencyImprovement := (oldState.AverageLatency - newState.AverageLatency) / oldState.AverageLatency
		latencyImprovementReward = latencyImprovement * 50 // 权重
	}

	// 吞吐量奖励
	throughputReward := newState.Throughput * 0.1 // 权重

	// 延迟稳定性奖励
	stdDevImprovementReward := 0.0
	if oldState.LatencyStdDev > 0 {
		stdDevImprovement := (oldState.LatencyStdDev - newState.LatencyStdDev) / oldState.LatencyStdDev
		stdDevImprovementReward = stdDevImprovement * 10 // 权重
	}

	// 切换惩罚
	switchPenalty := 0.0
	if actionChanged {
		switchPenalty = -agent.switchPenalty
	}

	totalReward := latencyImprovementReward + throughputReward + stdDevImprovementReward + switchPenalty

	log.Debugf("PPO计算奖励: LatencyImpr=%.2f, Throughput=%.2f, StdDevImpr=%.2f, SwitchPenalty=%.2f, Total=%.2f",
		latencyImprovementReward, throughputReward, stdDevImprovementReward, switchPenalty, totalReward)

	return totalReward
}

// 预测动作概率
func (agent *PPOAgent) predictPolicyProbs(state State) []float64 {
	features := state.ToFeatures()
	agent.network.RLock()
	defer agent.network.RUnlock()

	logits := make([]float64, len(agent.network.Protocols))

	// 计算线性组合 (logits)
	for i := range logits {
		for j, feature := range features {
			logits[i] += agent.network.PolicyWeights[j][i] * feature
		}
	}

	// Softmax转换为概率
	probs := softmax(logits)
	return probs
}

// 预测状态值
func (agent *PPOAgent) predictValue(state State) float64 {
	features := state.ToFeatures()
	agent.network.RLock()
	defer agent.network.RUnlock()

	value := 0.0
	for i, feature := range features {
		value += agent.network.ValueWeights[i] * feature
	}
	return value
}

// Softmax函数
func softmax(x []float64) []float64 {
	result := make([]float64, len(x))
	sum := 0.0
	max := -math.MaxFloat64

	// 找到最大值，避免溢出
	for _, v := range x {
		if v > max {
			max = v
		}
	}

	// 计算exp并求和
	for i, v := range x {
		exp := math.Exp(v - max)
		result[i] = exp
		sum += exp
	}

	// 归一化
	if sum > 0 {
		for i := range result {
			result[i] /= sum
		}
	} else {
		// 如果所有值都是-inf，使用均匀分布
		for i := range result {
			result[i] = 1.0 / float64(len(result))
		}
	}

	return result
}

// 根据概率选择动作
func (agent *PPOAgent) selectAction(probs []float64) (int, string) {
	r := rand.Float64()
	cumProb := 0.0
	for i, prob := range probs {
		cumProb += prob
		if r <= cumProb {
			return i, agent.network.Protocols[i]
		}
	}
	// 安全措施，应该不会执行到这里
	lastIdx := len(probs) - 1
	return lastIdx, agent.network.Protocols[lastIdx]
}

// 选择下一个协议
func (agent *PPOAgent) selectNextProtocol(state State) string {
	// 计算策略网络输出的动作概率
	actionProbs := agent.predictPolicyProbs(state)

	// 选择动作（协议）
	_, action := agent.selectAction(actionProbs)

	// 存储当前状态和动作（用于后续训练）
	if agent.trainingEnabled {
		agent.network.Lock()
		agent.network.States = append(agent.network.States, state.ToFeatures())
		agent.network.ActionProbs = append(agent.network.ActionProbs, actionProbs)
		agent.network.Actions = append(agent.network.Actions, agent.network.ActionIndices[action])
		agent.network.Unlock()
	}

	return action
}

// 训练PPO网络
func (agent *PPOAgent) update(lastState State, lastAction string, reward float64, currentState State) {
	if !agent.trainingEnabled || len(agent.network.States) < agent.batchSize {
		return
	}

	agent.network.Lock()
	defer agent.network.Unlock()

	// 添加最新的奖励和下一状态
	agent.network.Rewards = append(agent.network.Rewards, reward)
	agent.network.NextStates = append(agent.network.NextStates, currentState.ToFeatures())

	// 如果收集的轨迹还不够一个批次，则等待
	if len(agent.network.States) < agent.batchSize {
		return
	}

	// 计算优势函数和回报
	advantages := make([]float64, len(agent.network.States))
	returns := make([]float64, len(agent.network.States))

	for i := len(agent.network.States) - 1; i >= 0; i-- {
		nextValue := 0.0
		if i < len(agent.network.States)-1 {
			// 转换为State以便调用predictValue
			nextState := State{}
			features := agent.network.NextStates[i]
			if len(features) >= 7 {
				nextState.RequestRate = features[0]
				nextState.AverageLatency = features[1]
				nextState.LatencyStdDev = features[2]
				nextState.Throughput = features[3]
				nextState.ReadWriteRatio = features[4]
				nextState.KeyDistribution = features[5]
				nextState.NetworkCondition = features[6]
			}
			nextValue = agent.predictValue(nextState)
		}

		// 转换为State以便调用predictValue
		curState := State{}
		features := agent.network.States[i]
		if len(features) >= 7 {
			curState.RequestRate = features[0]
			curState.AverageLatency = features[1]
			curState.LatencyStdDev = features[2]
			curState.Throughput = features[3]
			curState.ReadWriteRatio = features[4]
			curState.KeyDistribution = features[5]
			curState.NetworkCondition = features[6]
		}

		currentValue := agent.predictValue(curState)

		// 假设一步奖励，简化计算
		reward := agent.network.Rewards[i]
		returns[i] = reward + agent.discountFactor*nextValue
		advantages[i] = returns[i] - currentValue
	}

	// 对优势函数进行标准化，提高训练稳定性
	advMean := 0.0
	advStd := 0.0
	for _, adv := range advantages {
		advMean += adv
	}
	advMean /= float64(len(advantages))

	for _, adv := range advantages {
		diff := adv - advMean
		advStd += diff * diff
	}
	advStd = math.Sqrt(advStd / float64(len(advantages)))

	if advStd > 0 {
		for i := range advantages {
			advantages[i] = (advantages[i] - advMean) / advStd
		}
	}

	// PPO训练循环
	for epoch := 0; epoch < agent.epochs; epoch++ {
		// 随机打乱训练数据
		indices := rand.Perm(len(agent.network.States))

		for _, idx := range indices {
			state := agent.network.States[idx]
			action := agent.network.Actions[idx]
			oldProbs := agent.network.ActionProbs[idx]
			advantage := advantages[idx]
			returnValue := returns[idx]

			// 简化版本：直接更新网络权重
			// 在实际中应使用深度学习库的优化器

			// 1. 策略网络更新
			newProbs := make([]float64, len(oldProbs))
			for i := range newProbs {
				// 计算策略网络输出
				newProbs[i] = oldProbs[i] // 简化，假设输出没变
			}

			// 计算策略比率
			ratio := newProbs[action] / oldProbs[action]

			// 计算裁剪后的目标
			clippedRatio := math.Max(
				math.Min(ratio, 1+agent.clipEpsilon),
				1-agent.clipEpsilon,
			)

			// 策略损失
			policyLoss := -math.Min(
				ratio*advantage,
				clippedRatio*advantage,
			)

			// 熵正则化
			entropyLoss := -calculateEntropy(newProbs) * agent.entropyCoeff

			// 对策略网络参数进行梯度下降
			for i, feature := range state {
				for j := range agent.network.PolicyWeights[i] {
					if j == action {
						// 简化的梯度更新，实际应使用自动微分
						agent.network.PolicyWeights[i][j] += agent.learningRate * advantage * feature
					}
				}
			}

			// 2. 值网络更新
			valueLoss := math.Pow(returnValue-agent.predictValue(State{
				RequestRate:      state[0],
				AverageLatency:   state[1],
				LatencyStdDev:    state[2],
				Throughput:       state[3],
				ReadWriteRatio:   state[4],
				KeyDistribution:  state[5],
				NetworkCondition: state[6],
			}), 2)

			// 对值网络参数进行梯度下降
			for i, feature := range state {
				valueGrad := 2 * (agent.predictValue(State{
					RequestRate:      state[0],
					AverageLatency:   state[1],
					LatencyStdDev:    state[2],
					Throughput:       state[3],
					ReadWriteRatio:   state[4],
					KeyDistribution:  state[5],
					NetworkCondition: state[6],
				}) - returnValue) * feature
				agent.network.ValueWeights[i] -= agent.learningRate * valueGrad
			}

			log.Debugf("PPO更新: epoch=%d, policyLoss=%.4f, valueLoss=%.4f, entropyLoss=%.4f",
				epoch, policyLoss, valueLoss, entropyLoss)
		}
	}

	// 清空轨迹数据，准备收集新一轮数据
	agent.network.States = agent.network.States[:0]
	agent.network.Actions = agent.network.Actions[:0]
	agent.network.ActionProbs = agent.network.ActionProbs[:0]
	agent.network.Rewards = agent.network.Rewards[:0]
	agent.network.NextStates = agent.network.NextStates[:0]
}

// 重置统计数据
func (agent *PPOAgent) resetStats() {
	agent.mu.Lock()
	defer agent.mu.Unlock()

	agent.requestCounter = 0
	agent.latencySum = 0
	agent.readCounter = 0
	agent.writeCounter = 0
	agent.keyAccess = make(map[paxi.Key]int)
	agent.latencyValues = agent.latencyValues[:0]
	agent.lastResetTime = time.Now()
}

// 保存模型
func (agent *PPOAgent) SaveModel(filename string) error {
	agent.network.RLock()
	defer agent.network.RUnlock()

	data := struct {
		PolicyWeights [][]float64    `json:"policy_weights"`
		ValueWeights  []float64      `json:"value_weights"`
		ActionIndices map[string]int `json:"action_indices"`
		LastAction    string         `json:"last_action"`
	}{
		PolicyWeights: agent.network.PolicyWeights,
		ValueWeights:  agent.network.ValueWeights,
		ActionIndices: agent.network.ActionIndices,
		LastAction:    agent.lastAction,
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化模型失败: %v", err)
	}

	return ioutil.WriteFile(filename, jsonData, 0644)
}

// 加载模型
func (agent *PPOAgent) LoadModel(filename string) error {
	jsonData, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("读取模型文件失败: %v", err)
	}

	var data struct {
		PolicyWeights [][]float64    `json:"policy_weights"`
		ValueWeights  []float64      `json:"value_weights"`
		ActionIndices map[string]int `json:"action_indices"`
		LastAction    string         `json:"last_action"`
	}

	if err := json.Unmarshal(jsonData, &data); err != nil {
		return fmt.Errorf("解析模型JSON失败: %v", err)
	}

	agent.network.Lock()
	defer agent.network.Unlock()

	agent.network.PolicyWeights = data.PolicyWeights
	agent.network.ValueWeights = data.ValueWeights
	agent.network.ActionIndices = data.ActionIndices
	agent.lastAction = data.LastAction

	return nil
}
