package main

import (
	"encoding/gob"
	"net"
	"testing"
	"time"

	"github.com/ailidani/paxi"
)

func TestServerHandleReply(t *testing.T) {
	// 创建测试服务器
	s := &server{}
	s.msgQueue = make(chan interface{}, 1024)
	s.stateMachine = paxi.NewDatabase()
	s.sender = paxi.NewSocket(paxi.ID("1.1"), paxi.GetConfig().Addrs)

	// 启动消息处理循环
	go s.loop()

	// 等待服务器启动
	time.Sleep(100 * time.Millisecond)

	// 创建测试Reply消息
	reply := paxi.Reply{
		Command: paxi.Command{
			Key:   1,
			Value: []byte("test value"),
		},
		Properties: make(map[string]string),
		Timestamp:  time.Now().UnixNano(),
	}

	// 连接到服务器
	conn, err := net.Dial("tcp", "127.0.0.1:1735")
	if err != nil {
		t.Fatalf("Failed to connect to server: %v", err)
	}
	defer conn.Close()

	// 发送Reply消息
	enc := gob.NewEncoder(conn)
	//convert reply to interface
	replyInterface := interface{}(reply)
	err = enc.Encode(&replyInterface)
	if err != nil {
		t.Fatalf("Failed to encode reply: %v", err)
	}

	// 等待消息处理
	time.Sleep(100 * time.Millisecond)

	// 检查消息队列中是否有消息
	select {
	case msg := <-s.msgQueue:
		receivedReply, ok := msg.(paxi.Reply)
		if !ok {
			t.Errorf("Expected Reply message, got %T", msg)
		}
		if string(receivedReply.Command.Value) != "test value" {
			t.Errorf("Expected value 'test value', got '%s'", string(receivedReply.Command.Value))
		}
	default:
		t.Error("No message received in queue")
	}
}
