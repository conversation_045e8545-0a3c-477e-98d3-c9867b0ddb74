package lib

import (
	"testing"
)

func Test(t *testing.T) {
	s := new(Stack)

	if s.<PERSON>() != 0 {
		t.<PERSON>("Length of an empty stack should be 0")
	}

	s.<PERSON>ush(1)

	if s.<PERSON>() != 1 {
		t.<PERSON>("Length should be 0")
	}

	if s.<PERSON><PERSON>().(int) != 1 {
		t.<PERSON>("Top item on the stack should be 1")
	}

	if s.Pop().(int) != 1 {
		t.<PERSON>("Top item should have been 1")
	}

	if s.<PERSON>() != 0 {
		t.<PERSON><PERSON>("Stack should be empty")
	}

	s.<PERSON>ush(1)
	s.<PERSON>ush(2)

	if s.<PERSON>() != 2 {
		t.<PERSON><PERSON><PERSON>("Length should be 2")
	}

	if s.<PERSON>eek().(int) != 2 {
		t.<PERSON>("Top of the stack should be 2")
	}
}
