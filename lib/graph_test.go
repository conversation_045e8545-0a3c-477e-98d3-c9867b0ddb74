package lib

import "testing"

func TestGraoh(t *testing.T) {
	g := NewGraph()
	g.Add(1)
	g.Add<PERSON><PERSON>(1, 2)
	g.AddEdge(1, 3)
	g.AddEdge(2, 4)

	bfs := []interface{}{1, 2, 3, 4}
	for i, v := range g.BFS(1) {
		if v != bfs[i] {
			t.Fatalf("graph BFS(1) = %v", g.<PERSON><PERSON>(1))
		}
	}

	g.Add<PERSON>dge(4, 3)
	g.Add<PERSON>dge(3, 2)
	if !g.<PERSON>c<PERSON>() {
		t.<PERSON><PERSON>("graph cannot detect cycle")
	}
}
