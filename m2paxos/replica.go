package m2paxos

import (
	"reflect"
	"sync"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/log"
)

// Replica is WPaxos replica node
type Replica struct {
	paxi.Node
	mu   sync.RWMutex
	paxi map[paxi.Key]*kpaxos
}

// NewReplica create new Replica instance
func NewReplica(id paxi.ID, sender paxi.Socket) *Replica {
	r := new(Replica)
	r.Node = paxi.NewNode(id, sender)
	r.paxi = make(map[paxi.Key]*kpaxos)

	r.Register(paxi.Request{}, r.HandleRequest)
	r.Register(Prepare{}, r.handlePrepare)
	r.Register(Promise{}, r.handlePromise)
	r.Register(Accept{}, r.handleAccept)
	r.Register(Accepted{}, r.handleAccepted)
	r.Register(Commit{}, r.handleCommit)
	r.Register(LeaderChange{}, r.handleLeaderChange)
	return r
}

func (r *Replica) init(key paxi.Key) {
	r.mu.Lock()
	defer r.mu.Unlock()
	
	if _, exists := r.paxi[key]; !exists {
		r.paxi[key] = newKPaxos(key, r.Node)
	}
}

func (r *Replica) HandleRequest(m paxi.Request) {
	log.Debugf("Replica %s received %v\n", r.ID(), m)
	key := m.Command.Key
	r.init(key)

	r.mu.RLock()
	p := r.paxi[key]
	r.mu.RUnlock()
	
	if p.IsLeader() || p.Ballot() == 0 {
		log.Debugf("Replica %s is leader, handle request %v\n", r.ID(), m)
		p.HandleRequest(m)
		to := p.Hit(m.NodeID)
		log.Debugf("Replica %s hit %s\n", r.ID(), to)
		if to != "" && to.Zone() != r.ID().Zone() {
			log.Debugf("Replica %s send leader change to %s\n", r.ID(), to)
			p.Send(to, LeaderChange{
				Key:    key,
				To:     to,
				From:   r.ID(),
				Ballot: p.Ballot(),
			})
		}
	} else {
		log.Debugf("Replica %s is not leader, forward request %v\n", r.ID(), m)
		go r.Forward(p.Leader(), m)
	}
}

func (r *Replica) handlePrepare(m Prepare) {
	log.Debugf("Replica %s ===[%v]===>>> Replica %s\n", m.Ballot.ID(), m, r.ID())
	r.init(m.Key)
	
	r.mu.RLock()
	r.paxi[m.Key].HandleP1a(m.P1a)
	r.mu.RUnlock()
}

func (r *Replica) handlePromise(m Promise) {
	log.Debugf("Replica %s ===[%v]===>>> Replica %s\n", m.ID, m, r.ID())
	r.mu.RLock()
	r.paxi[m.Key].HandleP1b(m.P1b)
	r.mu.RUnlock()
}

func (r *Replica) handleAccept(m Accept) {
	log.Debugf("Replica %s ===[%v]===>>> Replica %s\n", m.Ballot.ID(), m, r.ID())
	r.init(m.Key)
	
	r.mu.RLock()
	r.paxi[m.Key].HandleP2a(m.P2a)
	r.mu.RUnlock()
}

func (r *Replica) handleAccepted(m Accepted) {
	log.Debugf("Replica %s ===[%v]===>>> Replica %s\n", m.ID, m, r.ID())
	r.mu.RLock()
	r.paxi[m.Key].HandleP2b(m.P2b)
	r.mu.RUnlock()
}

func (r *Replica) handleCommit(m Commit) {
	log.Debugf("Replica %s ===[%v]===>>> Replica %s\n", m.Ballot.ID(), m, r.ID())
	r.init(m.Key)
	
	r.mu.RLock()
	r.paxi[m.Key].HandleP3(m.P3)
	r.mu.RUnlock()
}

func (r *Replica) handleLeaderChange(m LeaderChange) {
	log.Debugf("Replica %s ===[%v]===>>> Replica %s\n", m.From, m, r.ID())
	r.mu.RLock()
	p := r.paxi[m.Key]
	r.mu.RUnlock()
	
	if m.Ballot == p.Ballot() && m.To == r.ID() {
		// log.Debugf("Replica %s : change leader of key %d\n", r.ID(), m.Key)
		p.P1a()
	}
}

func (r *Replica) Ask(m interface{}) bool {
	msgType := reflect.TypeOf(m)
	handles := r.Node.GetHandles()
	for t := range handles {
		if t == msgType.String() {
			log.Debugf("Replica %s ask %v\n", r.ID(), m)
			r.Node.SendMessage(m)
			return true
		}
	}
	return false
}
