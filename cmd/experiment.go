package main

import (
	"flag"
	"os"
	"time"

	"github.com/ailidani/paxi/experiments"
	"github.com/ailidani/paxi/log"
)

func main() {
	// 命令行参数
	var (
		experimentType = flag.String("type", "full", "实验类型: full, baseline, ppo, ablation")
		outputDir      = flag.String("output", "./experiment_results", "输出目录")
		configFile     = flag.String("config", "", "配置文件路径")
		verbose        = flag.Bool("verbose", false, "详细输出")
	)
	flag.Parse()

	// 设置日志级别
	if *verbose {
		log.SetLevel(log.DEBUG)
	}

	// 确保输出目录存在
	if err := os.MkdirAll(*outputDir, 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	// 切换到输出目录
	if err := os.Chdir(*outputDir); err != nil {
		log.Fatalf("切换到输出目录失败: %v", err)
	}

	log.Infof("=== PPO共识协议切换性能评估实验 ===")
	log.Infof("实验类型: %s", *experimentType)
	log.Infof("输出目录: %s", *outputDir)

	// 创建实验运行器
	runner := experiments.NewExperimentRunner()

	// 根据实验类型运行相应的实验
	switch *experimentType {
	case "full":
		runFullExperiment(runner)
	case "baseline":
		runBaselineExperiment(runner)
	case "ppo":
		runPPOExperiment(runner)
	case "ablation":
		runAblationExperiment(runner)
	default:
		log.Fatalf("未知的实验类型: %s", *experimentType)
	}

	log.Infof("=== 实验完成 ===")
}

// runFullExperiment 运行完整实验套件
func runFullExperiment(runner *experiments.ExperimentRunner) {
	log.Infof("运行完整实验套件")

	startTime := time.Now()

	if err := runner.RunFullExperimentSuite(); err != nil {
		log.Fatalf("完整实验套件失败: %v", err)
	}

	duration := time.Since(startTime)
	log.Infof("完整实验套件耗时: %v", duration)
}

// runBaselineExperiment 仅运行基准测试
func runBaselineExperiment(runner *experiments.ExperimentRunner) {
	log.Infof("运行基准测试")

	config := experiments.GetExperimentConfig()
	framework := experiments.NewExperimentFramework(config)

	workloads := experiments.GetStandardWorkloadPatterns()[:5] // 静态负载

	for _, workload := range workloads {
		log.Infof("基准测试: %s", workload.Name)

		result, err := framework.RunExperiment(experiments.BaselineExperiment, workload)
		if err != nil {
			log.Errorf("基准实验失败: %v", err)
			continue
		}

		// 保存结果
		filename := "baseline_" + workload.Name + ".json"
		experiments.SaveJSON(result, filename)

		log.Infof("基准测试 %s 完成", workload.Name)
	}
}

// runPPOExperiment 仅运行PPO测试
func runPPOExperiment(runner *experiments.ExperimentRunner) {
	log.Infof("运行PPO测试")

	config := experiments.GetExperimentConfig()
	framework := experiments.NewExperimentFramework(config)

	workloads := experiments.GetStandardWorkloadPatterns()

	for _, workload := range workloads {
		log.Infof("PPO测试: %s", workload.Name)

		result, err := framework.RunExperiment(experiments.PPOExperiment, workload)
		if err != nil {
			log.Errorf("PPO实验失败: %v", err)
			continue
		}

		// 保存结果
		filename := "ppo_" + workload.Name + ".json"
		experiments.SaveJSON(result, filename)

		log.Infof("PPO测试 %s 完成", workload.Name)
	}
}

// runAblationExperiment 仅运行消融实验
func runAblationExperiment(runner *experiments.ExperimentRunner) {
	log.Infof("运行消融实验")

	config := experiments.GetExperimentConfig()
	framework := experiments.NewExperimentFramework(config)

	// 使用动态负载
	workload := experiments.GetStandardWorkloadPatterns()[5]

	result, err := framework.RunExperiment(experiments.AblationExperiment, workload)
	if err != nil {
		log.Fatalf("消融实验失败: %v", err)
	}

	// 保存结果
	experiments.SaveJSON(result, "ablation_results.json")

	log.Infof("消融实验完成")
}
