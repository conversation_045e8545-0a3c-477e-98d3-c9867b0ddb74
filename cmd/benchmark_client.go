package main

import (
	"encoding/binary"
	"encoding/json"
	"flag"
	"fmt"
	"math"
	"os"
	"time"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/chain"
	"github.com/ailidani/paxi/paxos"
)

var id = flag.String("id", "", "node id this client connects to")
var algorithm = flag.String("algorithm", "", "Client API type [paxos, chain]")
var load = flag.Bool("load", false, "Load K keys into DB")
var master = flag.String("master", "", "Master address.")
var output = flag.String("output", "", "Output file for performance metrics (JSON format)")
var workload = flag.String("workload", "mixed_read_write", "Workload type")

// PerformanceMetrics 存储性能指标
type PerformanceMetrics struct {
	ExperimentType      string  `json:"experiment_type"`
	WorkloadType        string  `json:"workload_type"`
	Protocol            string  `json:"protocol"`
	AverageLatencyMs    float64 `json:"average_latency_ms"`
	ThroughputOpsPerSec float64 `json:"throughput_ops_per_sec"`
	P99LatencyMs        float64 `json:"p99_latency_ms"`
	LatencyStdDevMs     float64 `json:"latency_std_dev_ms"`
	TotalOperations     int     `json:"total_operations"`
	Duration            float64 `json:"duration_seconds"`
	ReadRatio           float64 `json:"read_ratio"`
	WriteRatio          float64 `json:"write_ratio"`
	Timestamp           string  `json:"timestamp"`
}

// db implements Paxi.DB interface for benchmarking
type db struct {
	paxi.Client
}

func (d *db) Init() error {
	return nil
}

func (d *db) Stop() error {
	return nil
}

func (d *db) Read(k int) (int, error) {
	key := paxi.Key(k)
	v, err := d.Get(key)
	if len(v) == 0 {
		return 0, nil
	}
	x, _ := binary.Uvarint(v)
	return int(x), err
}

func (d *db) Write(k, v int) error {
	key := paxi.Key(k)
	value := make([]byte, binary.MaxVarintLen64)
	binary.PutUvarint(value, uint64(v))
	err := d.Put(key, value)
	return err
}

// 计算标准差
func calculateStdDev(data []float64, mean float64) float64 {
	sum := 0.0
	for _, v := range data {
		sum += (v - mean) * (v - mean)
	}
	return math.Sqrt(sum / float64(len(data)))
}

func main() {
	paxi.Init()

	if *master != "" {
		paxi.ConnectToMaster(*master, true, paxi.ID(*id))
	}

	d := new(db)
	switch *algorithm {
	case "paxos":
		d.Client = paxos.NewClient(paxi.ID(*id))
	case "chain":
		d.Client = chain.NewClient()
	default:
		d.Client = paxi.NewHTTPClient(paxi.ID(*id))
	}

	b := paxi.NewBenchmark(d)
	if *load {
		b.Load()
	} else {
		startTime := time.Now()
		b.Run(*id)
		duration := time.Since(startTime)

		// 计算性能指标
		if *output != "" {
			stat := paxi.Statistic(b.GetLatencies())
			
			// 计算标准差
			latencies := make([]float64, len(b.GetLatencies()))
			for i, l := range b.GetLatencies() {
				latencies[i] = float64(l.Nanoseconds()) / 1000000.0
			}
			stdDev := calculateStdDev(latencies, stat.Mean)

			config := paxi.GetConfig()
			metrics := PerformanceMetrics{
				ExperimentType:      "benchmark",
				WorkloadType:        *workload,
				Protocol:            getProtocolFromEnv(),
				AverageLatencyMs:    stat.Mean,
				ThroughputOpsPerSec: float64(len(b.GetLatencies())) / duration.Seconds(),
				P99LatencyMs:        stat.P99,
				LatencyStdDevMs:     stdDev,
				TotalOperations:     len(b.GetLatencies()),
				Duration:            duration.Seconds(),
				ReadRatio:           1.0 - config.Benchmark.W,
				WriteRatio:          config.Benchmark.W,
				Timestamp:           time.Now().Format(time.RFC3339),
			}

			// 输出到JSON文件
			file, err := os.Create(*output)
			if err != nil {
				fmt.Printf("Error creating output file: %v\n", err)
				return
			}
			defer file.Close()

			encoder := json.NewEncoder(file)
			encoder.SetIndent("", "  ")
			if err := encoder.Encode(metrics); err != nil {
				fmt.Printf("Error encoding metrics: %v\n", err)
			}
		}
	}
}

func getProtocolFromEnv() string {
	if protocol := os.Getenv("FIXED_PROTOCOL"); protocol != "" {
		return protocol
	}
	if picker := os.Getenv("PROTOCOL_PICKER"); picker == "ppo" {
		return "PPO"
	}
	return "default"
}
