package paxi

import (
	"net/http"
	"reflect"
	"sync"

	"github.com/ailidani/paxi/log"
)



// Node is the primary access point for every replica
// it includes networking, state machine and RESTful API server
type Node interface {
	Socket
	Database
	ID() ID
	Run()
	Retry(r Request)
	Forward(id ID, r Request)
	Register(m interface{}, f interface{})
	GetHandles() map[string]reflect.Value
	SendMessage(m interface{})
}

// node implements Node interface
type node struct {
	id ID

	Socket
	Database
	MessageChan chan interface{}
	handles     map[string]reflect.Value
	server      *http.Server

	sync.RWMutex
	forwards map[string]*Request
}

// NewNode creates a new Node object from configuration
func NewNode(id ID, sender Socket) Node {
	return &node{
		id:          id,
		Socket:      sender,
		Database:    NewDatabase(),
		MessageChan: make(chan interface{}, config.ChanBufferSize),
		handles:     make(map[string]reflect.Value),
		forwards:    make(map[string]*Request),
	}
}

func (n *node) ID() ID {
	return n.id
}

func (n *node) Retry(r Request) {
	log.Debugf("node %v retry reqeust %v", n.id, r)
	n.MessageChan <- r
}

// Register a handle function for each message type
func (n *node) Register(m interface{}, f interface{}) {
	t := reflect.TypeOf(m)
	fn := reflect.ValueOf(f)
	//print node.handles
	if fn.Kind() != reflect.Func || fn.Type().NumIn() != 1 || fn.Type().In(0) != t {
		panic("register handle function error")
	}
	n.handles[t.String()] = fn
}

func (n *node) GetHandles() map[string]reflect.Value {
	return n.handles
}

// Run start and run the node
func (n *node) Run() {
	log.Infof("node %v start running", n.id)
	if len(n.handles) > 0 {
		go n.handle()
		go n.recv()
	}
	// n.http()
}

// recv receives messages from socket and pass to message channel
func (n *node) recv() {
	for {
		m := n.Recv()
		log.Debugf("node %v recv message %v", n.id, m)
		switch m := m.(type) {
		case Request:
			m.C = make(chan Reply, 1)
			go func(r Request) {
				n.Send(r.NodeID, <-r.C)
			}(m)
			n.MessageChan <- m
			continue

		case Reply:
			n.RLock()
			r := n.forwards[m.Command.String()]
			log.Debugf("node %v received reply %v", n.id, m)
			n.RUnlock()
			r.Reply(m)
			continue
		}
		n.MessageChan <- m
	}
}

// handle receives messages from message channel and calls handle function using refection
func (n *node) handle() {
	for {
		msg := <-n.MessageChan
		v := reflect.ValueOf(msg)
		name := v.Type().String()
		f, exists := n.handles[name]
		if !exists {
			log.Debugf("node %v handles: %v", n.id, n.handles)
			log.Fatalf("no registered handle function for message type %v", name)
		}
		log.Infof("node %v handle message %v", n.id, msg)
		f.Call([]reflect.Value{v})
	}
}

// func (n *node) Forward(id ID, m Request) {
// 	key := m.Command.Key
// 	url := config.HTTPAddrs[id] + "/" + strconv.Itoa(int(key))

// 	log.Debugf("Node %v forwarding %v to %s", n.ID(), m, id)

// 	method := http.MethodGet
// 	var body io.Reader
// 	if !m.Command.IsRead() {
// 		method = http.MethodPut
// 		body = bytes.NewBuffer(m.Command.Value)
// 	}
// 	req, err := http.NewRequest(method, url, body)
// 	if err != nil {
// 		log.Error(err)
// 		return
// 	}
// 	req.Header.Set(HTTPClientID, string(n.id))
// 	req.Header.Set(HTTPCommandID, strconv.Itoa(m.Command.CommandID))
// 	res, err := http.DefaultClient.Do(req)
// 	if err != nil {
// 		log.Error(err)
// 		m.Reply(Reply{
// 			Command: m.Command,
// 			Err:     err,
// 		})
// 		return
// 	}
// 	defer res.Body.Close()
// 	if res.StatusCode == http.StatusOK {
// 		b, err := ioutil.ReadAll(res.Body)
// 		if err != nil {
// 			log.Error(err)
// 		}
// 		m.Reply(Reply{
// 			Command: m.Command,
// 			Value:   Value(b),
// 		})
// 	} else {
// 		m.Reply(Reply{
// 			Command: m.Command,
// 			Err:     errors.New(res.Status),
// 		})
// 	}
// }

// Forward方法在Paxos协议中用于将请求转发给当前leader节点
// 1. 当节点发现自己不是leader时，会将客户端请求转发给当前leader
// 2. 记录调试日志以便追踪请求转发过程
// 3. 设置请求的源节点ID，用于leader处理完请求后能正确返回响应
// 4. 将请求存入转发缓存，用于后续处理leader的响应
// 5. 通过Send方法将请求发送给目标leader节点
func (n *node) Forward(id ID, m Request) {
	log.Debugf("Node %v forwarding %v to %s", n.ID(), m, id)
	m.NodeID = n.id
	n.Lock()
	n.forwards[m.Command.String()] = &m
	n.Unlock()
	n.Send(id, m)
}

func (n *node) SendMessage(msg interface{}) {
	log.Debugf("Node %v send message %v", n.ID(), msg)
	n.MessageChan <- msg
}
