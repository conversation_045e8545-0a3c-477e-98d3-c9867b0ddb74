package paxi

import (
	"encoding/gob"
	"fmt"
	"net"
	"time"

	"github.com/ailidani/paxi/log"
)

var CurrentNodeID ID

func init() {
	gob.Register(Request{})
	gob.Register(Reply{})
	gob.Register(Read{})
	gob.Register(ReadReply{})
	gob.Register(Transaction{})
	gob.Register(TransactionReply{})
	gob.Register(Register{})
	gob.Register(Config{})
}

/***************************
 * Client-Replica Messages *
 ***************************/

// Request is client reqeust with http response channel
type Request struct {
	Command    Command
	Properties map[string]string
	Timestamp  int64
	NodeID     ID         // forward by node
	C          chan Reply // reply channel created by request receiver
}

// Reply replies to current client session
func (r *Request) Reply(reply Reply) {
	//log id reply C
	// if Node != self id
	if r.NodeID != GetCurrentNodeID() {
		//send back to NodeID
		conn, err := net.Dial("tcp", GetConfig().Addrs[r.NodeID])
		localaddr := conn.LocalAddr()
		log.Debugf("Dial %s from %s", GetConfig().Addrs[r.NodeID], localaddr)

		if err != nil {
			log.Errorf("Dial error %v", err)
			return
		}
		defer conn.Close()
		enc := gob.NewEncoder(conn)
		log.Debugf("Send reply %v to %s", reply, r.NodeID)
		replyInterface := interface{}(reply)
		reply.Command.McpSeqNo = r.Command.McpSeqNo
		reply.Command.McpProcessedBy = r.Command.McpProcessedBy
		err = enc.Encode(&replyInterface)
		if err != nil {
			log.Errorf("Encode error %v", err)
			return
		}
		log.Debugf("Send reply %v to %s", reply, r.NodeID)
		return
	}
	log.Debugf("Reply from %s msg %v", r.NodeID, r.C)
	r.C <- reply
}

func (r Request) String() string {
	return fmt.Sprintf("Request {cmd=%v nid=%v c=%v}", r.Command, r.NodeID, r.C)
}

// Reply includes all info that might replies to back the client for the coresponding reqeust
type Reply struct {
	Command    Command
	Value      Value
	Properties map[string]string
	Timestamp  int64
	Err        error
	Latency    time.Duration
}

func (r Reply) String() string {
	return fmt.Sprintf("Reply {cmd=%v value=%x prop=%v}", r.Command, r.Value, r.Properties)
}

// Read can be used as a special request that directly read the value of key without go through replication protocol in Replica
type Read struct {
	CommandID int
	Key       Key
}

func (r Read) String() string {
	return fmt.Sprintf("Read {cid=%d, key=%d}", r.CommandID, r.Key)
}

// ReadReply cid and value of reading key
type ReadReply struct {
	CommandID int
	Value     Value
}

func (r ReadReply) String() string {
	return fmt.Sprintf("ReadReply {cid=%d, val=%x}", r.CommandID, r.Value)
}

// Transaction contains arbitrary number of commands in one request
// TODO read-only or write-only transactions
type Transaction struct {
	Commands  []Command
	Timestamp int64

	c chan TransactionReply
}

// Reply replies to current client session
func (t *Transaction) Reply(r TransactionReply) {
	t.c <- r
}

func (t Transaction) String() string {
	return fmt.Sprintf("Transaction {cmds=%v}", t.Commands)
}

// TransactionReply is the result of transaction struct
type TransactionReply struct {
	OK        bool
	Commands  []Command
	Timestamp int64
	Err       error
}

/**************************
 *     Config Related     *
 **************************/

// Register message type is used to regitster self (node or client) with master node
type Register struct {
	Client bool
	ID     ID
	Addr   string
}

func GetCurrentNodeID() ID {
	return CurrentNodeID
}

func SetCurrentNodeID(id ID) {
	CurrentNodeID = id
}
