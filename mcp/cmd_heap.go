package mcp

import "github.com/ailidani/paxi"

// CmdHeap Command的小顶堆
type CmdHeap []paxi.Command

func (h CmdHeap) Len() int {
	return len(h)
}

func (h CmdHeap) Less(i, j int) bool {
	return h[i].McpSeqNo < h[j].McpSeqNo
}

func (h CmdHeap) Swap(i, j int) {
	h[i], h[j] = h[j], h[i]
}

func (h *CmdHeap) Push(x interface{}) {
	*h = append(*h, x.(paxi.Command))
}

func (h *CmdHeap) Pop() interface{} {
	old := *h
	n := len(old)
	x := old[n-1]
	*h = old[:n-1]
	return x
}
