package mcp

import (
	"container/heap"
	"sync"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/log"
)

// 元一致性协议
type Mcp struct {
	currCP          string       // 当前正在使用的共识协议
	currCPLock      sync.RWMutex // 共识协议读写锁
	seqNo           int          // 自增序列号
	seqNoLock       sync.RWMutex // 序列号读写锁
	pendingCommands CmdHeap      // 待执行命令缓冲区
	executeLock     sync.RWMutex // 待执行命令的同步锁
	currSeqNo       int          // 状态机正在执行的命令序列号
	sys_overhead    int          // overhead
	sys_p90         int          // p90
	sys_p99         int          // p99
}

// NewMcp 构建Mcp
func NewMcp() *Mcp {
	mcp := Mcp{
		currCP:          "paxos",
		seqNo:           1,
		pendingCommands: CmdHeap{},
	}
	heap.Init(&mcp.pendingCommands)
	return &mcp
}

func (mcp *Mcp) Init() {
	mcp.SetSysOverhead(10)
	mcp.SetSysP90(150)
	mcp.SetSysP99(200)

}

// GetCurrCP 获取当前正在运行的共识协议
func (mcp *Mcp) GetCurrCP() string {
	mcp.currCPLock.RLock()
	defer mcp.currCPLock.RUnlock()
	return mcp.currCP
}

// SetCurrCP 改变当前正在运行的共识协议
func (mcp *Mcp) SetCurrCP(newCP string) {
	mcp.currCPLock.Lock()
	defer mcp.currCPLock.Unlock()
	mcp.currCP = newCP
}

func (mcp *Mcp) GetSysOverhead() int {
	mcp.currCPLock.RLock()
	defer mcp.currCPLock.RUnlock()
	return mcp.sys_overhead
}

func (mcp *Mcp) SetSysOverhead(newOverhead int) {
	mcp.currCPLock.Lock()
	defer mcp.currCPLock.Unlock()
	mcp.sys_overhead = newOverhead
}

func (mcp *Mcp) getSysP90() int {
	mcp.currCPLock.RLock()
	defer mcp.currCPLock.RUnlock()
	return mcp.sys_p90
}

func (mcp *Mcp) SetSysP90(newP90 int) {
	mcp.currCPLock.Lock()
	defer mcp.currCPLock.Unlock()
	mcp.sys_p90 = newP90
}

func (mcp *Mcp) getSysP99() int {
	mcp.currCPLock.RLock()
	defer mcp.currCPLock.RUnlock()
	return mcp.sys_p99
}

func (mcp *Mcp) SetSysP99(newP99 int) {
	mcp.currCPLock.Lock()
	defer mcp.currCPLock.Unlock()
	mcp.sys_p99 = newP99
}

// AddSeqNo 为每一个客户端请求添加序列号
func (mcp *Mcp) AddSeqNo(clientRequest *paxi.Request) {
	mcp.seqNoLock.Lock()
	seqNo := mcp.seqNo
	mcp.seqNo++
	clientRequest.Command.McpSeqNo = seqNo
	mcp.seqNoLock.Unlock()
}

// AddProcessor 为每一个客户端请求添加一致性协议标签
func (mcp *Mcp) AddProcessor(clientRequest *paxi.Request, cp string) {
	clientRequest.Command.McpProcessedBy = cp
}

// ProxyExecute 通过元协议执行更新状态机状态
func (mcp *Mcp) ProxyExecute(cmd paxi.Command, cpExecFunc func(paxi.Command)) {
	mcp.executeLock.Lock()
	defer mcp.executeLock.Unlock()

	// 查看是否是序列中的下一个
	if cmd.McpSeqNo == mcp.currSeqNo+1 {
		// 是序列中的下一个，直接执行
		mcp.currSeqNo++
		cpExecFunc(cmd)
		// 查看缓冲区是否有可执行的
		for mcp.pendingCommands.Len() > 0 {
			c := mcp.pendingCommands[0]
			if c.McpSeqNo == mcp.currSeqNo+1 {
				// 执行
				cpExecFunc(cmd)
				heap.Pop(&mcp.pendingCommands)
				mcp.currSeqNo++
			} else {
				if c.McpSeqNo < mcp.currSeqNo+1 {
					// 理论上不存在
					log.Errorf("Mcp: Error state, <pending_cmd_seq_no=%v, mcp_seq_no=%v, cmd=%v>", c.McpSeqNo, mcp.currSeqNo, c)
				}
				break
			}
		}
	} else if cmd.McpSeqNo > mcp.currSeqNo+1 {
		// 不是序列中的下一个，放入待执行缓冲区
		heap.Push(&mcp.pendingCommands, cmd)
	} else {
		// 理论上不存在这种情况
		// log.Errorf("Mcp: Error command, <cmd_seq_no=%v,curr_seq_no=%v,cmd=%v>", cmd.McpSeqNo, mcp.currSeqNo, cmd)
	}
}
