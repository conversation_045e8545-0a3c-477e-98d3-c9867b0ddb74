---
description:
globs:
alwaysApply: false
---
# Paxi 协议切换机制

## 切换原理

协议切换层是Paxi项目的核心创新点，它能够根据工作负载特性动态选择最合适的共识协议。

## 核心组件

协议切换实现依赖于以下组件：
- **监控指标收集**：收集各种性能指标（如请求延迟、吞吐量、冲突率）
- **决策逻辑**：基于收集的指标决定何时切换以及切换到哪种协议
- **切换执行**：实现从一种协议到另一种协议的平滑转换

## 性能特性

不同协议在不同负载下有各自的性能优势：
- **Paxos**：结构简单，适合轻负载
- **EPaxos**：适合低冲突率的负载
- **M2Paxos**：适合有多个热点区域的负载
- **VPaxos**：适合垂直分片的数据结构
- **WPaxos**：适合广域网环境

## 实验计划

参见 [Todo.md](mdc:Todo.md) 了解当前的实验计划，包括基准测试、动态负载测试和切换开销评估。
