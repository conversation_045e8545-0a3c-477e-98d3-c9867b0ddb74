---
description:
globs:
alwaysApply: false
---
# Paxi 核心概念

## 基础架构

- **Node**：系统中的一个节点，实现在 [node.go](mdc:node.go)
- **Socket**：网络通信接口，实现在 [socket.go](mdc:socket.go)
- **Transport**：传输层抽象，实现在 [transport.go](mdc:transport.go)
- **Database**：状态机接口，实现在 [db.go](mdc:db.go)
- **Config**：系统配置，实现在 [config.go](mdc:config.go)

## 消息类型

- **Request**：客户端请求
- **Reply**：节点响应
- **Message**：节点间通信的基本单元

## 共识协议

每种共识协议都有各自的实现目录，共享基础框架但有不同的算法特性：

- **Paxos**: 经典的单领导者共识协议
- **EPaxos**: 无领导者共识协议，优化冲突少的情况
- **M2Paxos**: 多领导者Paxos变体
- **VPaxos**: 针对垂直分片优化的Paxos变体
- **WPaxos**: 适用于广域网的Paxos变体

## 协议切换

系统能够在运行时根据工作负载特性动态切换使用的共识协议，以获得最佳性能。
