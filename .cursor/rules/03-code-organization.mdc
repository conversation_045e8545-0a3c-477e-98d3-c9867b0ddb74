---
description:
globs:
alwaysApply: false
---
# Paxi 代码组织结构

## 基础包

根目录 `paxi` 包含：
- [node.go](mdc:node.go): 节点抽象
- [config.go](mdc:config.go): 配置管理
- [socket.go](mdc:socket.go): 通信接口
- [transport.go](mdc:transport.go): 网络传输
- [db.go](mdc:db.go): 状态机
- [message.go](mdc:message.go): 消息定义
- [id.go](mdc:id.go): ID定义
- [quorum.go](mdc:quorum.go): 法定人数实现

## 共识协议实现

每个协议在独立目录中实现：
- [paxos/](mdc:paxos/): 经典Paxos协议
- [epaxos/](mdc:epaxos/): EPaxos协议
- [m2paxos/](mdc:m2paxos/): M2Paxos协议
- [vpaxos/](mdc:vpaxos/): VPaxos协议
- [wpaxos/](mdc:wpaxos/): WPaxos协议

## 辅助工具

- [client/](mdc:client/): 客户端实现
- [bin/](mdc:bin/): 可执行文件
- [cmd/](mdc:cmd/): 命令行工具
- [log/](mdc:log/): 日志库

## 测试文件

测试文件通常与被测试的Go文件放在同一目录，并遵循 `*_test.go` 命名约定。例如：
- [socket_test.go](mdc:socket_test.go)
- [benchmark_test.go](mdc:benchmark_test.go)
