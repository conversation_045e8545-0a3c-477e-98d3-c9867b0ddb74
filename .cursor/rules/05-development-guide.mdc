---
description:
globs:
alwaysApply: false
---
# Paxi 开发指南

## 代码风格

- 遵循Go语言标准代码风格
- 使用清晰的函数和变量命名
- 为公共API添加文档注释
- 每个文件专注于一个功能模块

## 测试规范

- 为每个公共函数编写单元测试
- 使用 `*_test.go` 文件组织测试代码
- 包含基准测试 [benchmark_test.go](mdc:benchmark_test.go)
- 执行端到端测试验证协议正确性

## 项目结构

- 尽量保持模块间的低耦合
- 通过接口抽象不同层次的功能
- 核心接口定义：
  - **Node** 接口：[node.go](mdc:node.go)
  - **Socket** 接口：[socket.go](mdc:socket.go)
  - **Database** 接口：[db.go](mdc:db.go)

## 开发重点

当前开发重点是完成协议切换层的实现和评估，具体任务包括：
1. 实现监控指标收集
2. 实现智能决策逻辑
3. 完成基准测试和动态负载测试
4. 优化切换开销

详细的开发计划请参考 [Todo.md](mdc:Todo.md)。
