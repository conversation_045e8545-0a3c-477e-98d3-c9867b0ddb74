---
description:
globs:
alwaysApply: false
---
# Paxi 项目概述

Paxi 是一个实现了多种分布式共识协议的 Go 语言库，主要包含 Paxos 及其多种变体的实现。该项目支持在运行时动态切换不同的共识协议，以适应不同的工作负载和网络环境。

## 主要组件

- 基础框架：[node.go](mdc:node.go), [config.go](mdc:config.go), [message.go](mdc:message.go)
- 共识协议实现：
  - 经典 Paxos：[paxos/](mdc:paxos/)
  - EPaxos (Egalitarian Paxos)：[epaxos/](mdc:epaxos/)
  - M2Paxos：[m2paxos/](mdc:m2paxos/)
  - VPaxos：[vpaxos/](mdc:vpaxos/)
  - WPaxos：[wpaxos/](mdc:wpaxos/)
- 共识协议切换层：实现了不同协议间的动态切换

## 开发计划

当前项目主要关注在实现共识协议切换层的实验，详细计划见 [Todo.md](mdc:Todo.md)。
