package paxi

import (
	"encoding/csv"
	"encoding/gob"
	"fmt"
	"net"
	"os"
	"sort"
	"strconv"
	"sync"
	"testing"
	"time"

	"github.com/ailidani/paxi/log"
)

func TestParallelRequests(t *testing.T) {
	// 测试参数
	workers := 1
	requestsPerWorker := 10000

	// 收集延迟数据的通道
	latencies := make(chan time.Duration, workers*requestsPerWorker)

	// 使用WaitGroup等待所有worker完成
	var wg sync.WaitGroup
	wg.Add(workers)
	gob.Register(Request{})
	gob.Register(Reply{})
	gob.Register(Command{})
	gob.Register(Value{})
	// 启动workers个goroutine并行发送请求
	for i := 0; i < workers; i++ {
		go func(workerID int) {
			defer wg.Done()

			for j := 0; j < requestsPerWorker; j++ {
				// 连接服务器
				conn, err := net.Dial("tcp", "127.0.0.1:1735")
				if err != nil {
					t.Logf("Worker %d 无法连接到服务器: %v", workerID, err)
					continue
				}

				// 创建编码器

				encoder := gob.NewEncoder(conn)
				decoder := gob.NewDecoder(conn)

				// 创建测试请求
				var request interface{} = Request{
					Command:    Command{Key: Key(workerID*100 + j), Value: []byte(fmt.Sprintf("test-%d-%d", workerID, j))},
					Properties: map[string]string{},
					Timestamp:  0,
					C:          make(chan Reply),
					NodeID:     ID("1.1"),
				}

				// 记录开始时间
				startTime := time.Now()

				// 发送请求
				err = encoder.Encode(&request)
				if err != nil {
					t.Logf("Worker %d 发送请求失败: %v", workerID, err)
					conn.Close()
					continue
				}

				// 接收响应
				var reply Reply
				err = decoder.Decode(&reply)
				if err != nil {
					t.Logf("Worker %d 接收响应失败: %v", workerID, err)
					conn.Close()
					continue
				}

				// 计算延迟并放入通道
				latency := time.Since(startTime)
				latencies <- latency

				// 关闭连接
				conn.Close()

				log.Debugf("Worker %d, 请求 %d: 延迟 = %v", workerID, j, latency)
			}
		}(i)
	}

	// 等待所有worker完成
	wg.Wait()
	close(latencies)

	// 收集所有延迟数据
	var allLatencies []time.Duration
	for latency := range latencies {
		allLatencies = append(allLatencies, latency)
	}

	// 对延迟进行排序以便计算百分位数
	sort.Slice(allLatencies, func(i, j int) bool {
		return allLatencies[i] < allLatencies[j]
	})

	// 计算统计数据
	totalRequests := len(allLatencies)
	var totalLatency time.Duration
	for _, latency := range allLatencies {
		totalLatency += latency
	}

	// 防止除零错误
	if totalRequests == 0 {
		t.Fatal("没有成功的请求")
	}

	// 计算统计指标
	avgLatency := totalLatency / time.Duration(totalRequests)
	minLatency := allLatencies[0]
	maxLatency := allLatencies[totalRequests-1]
	medianLatency := allLatencies[totalRequests/2]
	p95Index := int(float64(totalRequests) * 0.95)
	p95Latency := allLatencies[p95Index]
	p99Index := int(float64(totalRequests) * 0.99)
	p99Latency := allLatencies[p99Index]

	// 输出统计表格
	t.Logf("\n延迟统计数据表格:")
	t.Logf("%-15s | %-15s", "指标", "值 (ms)")
	t.Logf("%-15s | %-15s", "---------------", "---------------")
	t.Logf("%-15s | %-15.2f", "总请求数", float64(totalRequests))
	t.Logf("%-15s | %-15.2f", "平均延迟", float64(avgLatency.Milliseconds()))
	t.Logf("%-15s | %-15.2f", "最小延迟", float64(minLatency.Milliseconds()))
	t.Logf("%-15s | %-15.2f", "最大延迟", float64(maxLatency.Milliseconds()))
	t.Logf("%-15s | %-15.2f", "中位数延迟", float64(medianLatency.Milliseconds()))
	t.Logf("%-15s | %-15.2f", "P95延迟", float64(p95Latency.Milliseconds()))
	t.Logf("%-15s | %-15.2f", "P99延迟", float64(p99Latency.Milliseconds()))

	// 保存延迟数据到CSV文件，用于生成图表
	saveLatenciesToCSV(allLatencies, t)

	t.Logf("延迟数据已保存到 latencies.csv 文件，可用于生成折线图")
	t.Logf("请使用 Excel、Python 等工具读取 CSV 文件并生成延迟折线图")
}

// 保存延迟数据到CSV文件
func saveLatenciesToCSV(latencies []time.Duration, t *testing.T) {
	file, err := os.Create("latencies.csv")
	if err != nil {
		t.Logf("创建CSV文件失败: %v", err)
		return
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// 写入表头
	err = writer.Write([]string{"请求编号", "延迟(毫秒)"})
	if err != nil {
		t.Logf("写入CSV表头失败: %v", err)
		return
	}

	// 写入延迟数据
	for i, latency := range latencies {
		err := writer.Write([]string{
			strconv.Itoa(i + 1),
			strconv.FormatInt(latency.Milliseconds(), 10),
		})
		if err != nil {
			t.Logf("写入CSV数据失败: %v", err)
			return
		}
	}
}
