package vpaxos

import (
	"flag"
	"reflect"
	"sync"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/log"
)

// master node id
var mid = paxi.ID(*flag.String("mid", "1.1", "Master ID in format of Zone.Node"))

type Replica struct {
	paxi.Node
	mu sync.RWMutex

	paxos  *paxos
	index  map[paxi.Key]paxi.Ballot
	policy map[paxi.Key]paxi.Policy

	pending map[paxi.Key][]paxi.Request
	master  *master
}

func NewReplica(id paxi.ID, sender paxi.Socket) *Replica {
	r := &Replica{
		Node:    paxi.NewNode(id, sender),
		index:   make(map[paxi.Key]paxi.Ballot),
		policy:  make(map[paxi.Key]paxi.Policy),
		pending: make(map[paxi.Key][]paxi.Request),
	}
	r.paxos = newPaxos(r.Node)

	if r.ID() == mid {
		r.master = newMaster(r)
	}

	r.Register(paxi.Request{}, r.HandleRequest)
	r.Register(Info{}, r.handleInfo)
	r.Register(P1a{}, r.handleP1a)
	return r
}

func (r *Replica) monitor(k paxi.Key, id paxi.ID) {
	r.mu.Lock()
	_, exist := r.policy[k]
	if !exist {
		r.policy[k] = paxi.NewPolicy()
	}
	policy := r.policy[k]
	r.mu.Unlock()

	to := policy.Hit(id)
	if to != "" && to.Zone() != r.ID().Zone() {
		move := Move{
			Key:  k,
			From: r.ID(),
			To:   to,
		}
		if r.master == nil {
			r.Send(mid, move)
		} else {
			r.master.handleMove(move)
		}
	}
}

func (r *Replica) HandleRequest(m paxi.Request) {
	log.Debugf("replica %v received %v", r.ID(), m)
	k := m.Command.Key

	r.mu.RLock()
	b, exist := r.index[k]
	r.mu.RUnlock()

	if exist {
		if b.ID() == r.ID() {
			r.paxos.handleRequest(m)
			r.monitor(k, m.NodeID)
		} else {
			r.Forward(b.ID(), m)
		}
	} else {
		if r.master == nil {
			r.mu.Lock()
			_, pendingExists := r.pending[k]
			if !pendingExists {
				r.pending[k] = make([]paxi.Request, 0)
			}
			r.pending[k] = append(r.pending[k], m)
			r.mu.Unlock()

			r.Send(mid, Query{
				Key: k,
				ID:  r.ID(),
			})
		} else {
			newB := r.master.query(k, r.ID())

			r.mu.Lock()
			r.index[k] = newB
			r.mu.Unlock()

			if newB.ID() == r.ID() {
				r.paxos.handleRequest(m)
				r.monitor(k, m.NodeID)
			} else {
				r.Forward(newB.ID(), m)
			}
		}
	}
}

func (r *Replica) handleInfo(m Info) {
	log.Debugf("replica %v received %v", r.ID(), m)

	var requestsToProcess []paxi.Request
	var needsP1a bool
	var zoneToMulticast int
	var ballotToSend paxi.Ballot

	r.mu.Lock()
	r.index[m.Key] = m.Ballot

	if pending, ok := r.pending[m.Key]; ok {
		requestsToProcess = make([]paxi.Request, len(pending))
		copy(requestsToProcess, pending)
		delete(r.pending, m.Key)
	}

	if m.Ballot.ID() == r.ID() {
		r.paxos.ballot = m.Ballot
		if m.OldBallot == 0 {
			r.paxos.active = true
		} else {
			r.paxos.active = false
			needsP1a = true
			zoneToMulticast = m.OldBallot.ID().Zone()
			ballotToSend = m.Ballot
		}
	}
	r.mu.Unlock()

	if needsP1a {
		r.MulticastZone(zoneToMulticast, P1a{
			Key:    m.Key,
			Ballot: ballotToSend,
		})
	}

	if len(requestsToProcess) > 0 {
		for _, request := range requestsToProcess {
			r.HandleRequest(request)
		}
	}
}

func (r *Replica) handleP1a(m P1a) {
	r.Send(m.Ballot.ID(), P1b{
		Key:    m.Key,
		Ballot: m.Ballot,
		ID:     r.ID(),
	})
}

func (r *Replica) Ask(m interface{}) bool {
	msgType := reflect.TypeOf(m)
	handles := r.Node.GetHandles()
	for t := range handles {
		if t == msgType.String() {
			log.Debugf("Replica %s ask %v\n", r.ID(), m)
			r.Node.SendMessage(m)
			return true
		}
	}
	return false
}
