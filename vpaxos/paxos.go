package vpaxos

import (
	"sync"

	"github.com/ailidani/paxi"
	"github.com/ailidani/paxi/log"
)

// entry in log
type entry struct {
	ballot  paxi.Ballot
	command paxi.Command
	commit  bool
	request *paxi.Request
	quorum  *paxi.Quorum
}

type paxos struct {
	paxi.Node
	mu      sync.Mutex
	log     map[int]*entry
	ballot  paxi.Ballot
	slot    int
	execute int
	active  bool

	pending []paxi.Request // pending during phase-1
	quorum  *paxi.Quorum
}

func newPaxos(node paxi.Node) *paxos {
	v := &paxos{
		Node: node,
		log:  make(map[int]*entry),
		// ballot: paxi.NewBallot(1, paxi.NewID(node.ID().Zone(), 1)),
		slot:    -1,
		pending: make([]paxi.Request, 0),
		quorum:  paxi.NewQuorum(),
	}

	v.Register(P1b{}, v.handleP1b)
	v.Register(P2a{}, v.handleP2a)
	v.Register(P2b{}, v.handleP2b)
	v.Register(P3{}, v.handleP3)

	return v
}

func (p *paxos) handleRequest(m paxi.Request) {
	p.mu.Lock()
	defer p.mu.Unlock()

	if !p.active {
		p.pending = append(p.pending, m)
		return
	}

	if p.ballot.ID() != p.ID() {
		p.Forward(p.ballot.ID(), m)
		return
	}

	p.slot++
	p.log[p.slot] = &entry{
		ballot:  p.ballot,
		command: m.Command,
		request: &m,
		quorum:  paxi.NewQuorum(),
	}
	p.log[p.slot].quorum.ACK(p.ID())
	p.MulticastZone(p.ID().Zone(), P2a{
		Ballot:  p.ballot,
		Slot:    p.slot,
		Command: m.Command,
	})
}

func (p *paxos) handleP1b(m P1b) {
	p.mu.Lock()
	defer p.mu.Unlock()

	log.Debugf("replica %v received %v", p.ID(), m)
	if p.active || m.Ballot < p.ballot {
		return
	}

	p.quorum.ACK(m.ID)
	if p.quorum.ZoneMajority() {
		p.quorum.Reset()
		p.active = true
		requestsToProcess := p.pending
		p.pending = nil

		for _, r := range requestsToProcess {
			if p.ballot.ID() != p.ID() {
				log.Warningf("Became active leader but ballot %s doesn't match ID %s", p.ballot.ID(), p.ID())
				continue
			}
			p.slot++
			p.log[p.slot] = &entry{
				ballot:  p.ballot,
				command: r.Command,
				request: &r,
				quorum:  paxi.NewQuorum(),
			}
			p.log[p.slot].quorum.ACK(p.ID())
			p.MulticastZone(p.ID().Zone(), P2a{
				Ballot:  p.ballot,
				Slot:    p.slot,
				Command: r.Command,
			})
		}
	}
}

func (p *paxos) handleP2a(m P2a) {
	p.mu.Lock()
	defer p.mu.Unlock()

	log.Debugf("replica %v received %v", p.ID(), m)
	if m.Ballot >= p.ballot {
		p.ballot = m.Ballot
		p.slot = paxi.Max(p.slot, m.Slot)
		e, exists := p.log[m.Slot]
		if !exists {
			e = &entry{}
			p.log[m.Slot] = e
		} else if e.commit {
			return
		}
		e.ballot = m.Ballot
		e.command = m.Command
		e.commit = false
	}

	p.Send(m.Ballot.ID(), P2b{
		Ballot: p.ballot,
		Slot:   m.Slot,
		ID:     p.ID(),
	})
}

func (p *paxos) handleP2b(m P2b) {
	p.mu.Lock()

	log.Debugf("replica %v received %v", p.ID(), m)
	entry, ok := p.log[m.Slot]
	if !ok {
		p.mu.Unlock()
		log.Debugf("replica %v ignored P2b for unknown slot %d", p.ID(), m.Slot)
		return
	}

	if m.Ballot < entry.ballot || entry.commit {
		p.mu.Unlock()
		return
	}

	if m.Ballot > p.ballot {
		p.ballot = m.Ballot
		p.active = (p.ballot.ID() == p.ID())
	}

	if m.Ballot.ID() == p.ID() && m.Ballot == entry.ballot {
		if entry.quorum == nil {
			entry.quorum = paxi.NewQuorum()
			entry.quorum.ACK(p.ID())
		}
		entry.quorum.ACK(m.ID)
		if entry.quorum.ZoneMajority() && !entry.commit {
			entry.commit = true
			cmd := entry.command
			slot := m.Slot
			ballot := m.Ballot
			p.mu.Unlock()

			p.MulticastZone(p.ID().Zone(), P3{
				Ballot:  ballot,
				Slot:    slot,
				Command: cmd,
			})

			p.exec()
			return
		}
	}
	p.mu.Unlock()
}

func (p *paxos) handleP3(m P3) {
	p.mu.Lock()

	log.Debugf("replica %v received %v", p.ID(), m)
	p.slot = paxi.Max(p.slot, m.Slot)
	e, exist := p.log[m.Slot]
	if !exist {
		p.log[m.Slot] = &entry{}
		e = p.log[m.Slot]
	} else if e.commit {
		p.mu.Unlock()
		return
	}

	e.ballot = m.Ballot
	e.command = m.Command
	e.commit = true

	p.mu.Unlock()

	p.exec()
}

func (p *paxos) exec() {
	for {
		var cmd paxi.Command
		var req *paxi.Request
		var executeSlot int

		p.mu.Lock()
		e, ok := p.log[p.execute]
		if !ok || !e.commit {
			p.mu.Unlock()
			break
		}

		cmd = e.command
		req = e.request
		executeSlot = p.execute
		p.execute++

		if e.request != nil {
			p.log[executeSlot].request = nil
		}

		p.mu.Unlock()

		value := p.Execute(cmd)

		if req != nil {
			req.Reply(paxi.Reply{
				Command: cmd,
				Value:   value,
			})
		}
	}
}
