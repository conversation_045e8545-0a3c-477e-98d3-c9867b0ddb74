# 共识协议切换层实验 ToDo 列表

## 实验目标
1. **展示基准性能**：量化 Paxos、EPaxos、M2Paxos 和 VPaxos 在不同类型和强度的固定负载下的吞吐量和延迟。
2. **评估切换开销**：测量或估计从一个协议切换到另一个协议所需的时间或性能影响。
3. **验证切换效果**：证明切换层能够根据负载特性选择合适的协议，并在动态变化的负载下获得比任何单一固定协议更好的综合性能。

## 实验设计
### 实验环境
- [✓] 部署多个节点（例如 3 或 5 个节点）运行 `server` 程序。  

- [✓] 开发或使用一个负载生成器客户端，支持：
  - 控制并发数或请求速率 (QPS)。
  - 配置读/写操作的比例。
  - 配置请求访问的 key 的模式（均匀分布或集中访问）。
  - 记录每个请求的发送时间和收到响应的时间。
  - 聚合统计数据（吞吐量、平均延迟、P99 延迟）。

### 基准测试 (Baseline Tests)
- [ ] 禁用协议切换层，固定使用某一种协议。
- [ ] 针对每种协议 (Paxos, EPaxos, M2Paxos, VPaxos)，分别运行以下负载测试：
  - 不同读写比（100% 读，50% 读/50% 写，100% 写）。
  - 不同冲突率（低冲突和高冲突场景）。
  - 不同请求速率 (QPS)，找到饱和点。
- [ ] 记录指标：吞吐量 (ops/sec)、平均延迟、P99 延迟。

### 动态负载与切换测试 (Dynamic Load & Switching Tests)
- [ ] 启用协议切换层。
- [ ] 设计动态变化的负载场景：
  - 场景 1：冲突率变化（低冲突 → 高冲突 → 低冲突）。
  - 场景 2：读写比变化（高比例读 → 高比例写 → 高比例读）。
  - 场景 3：负载强度变化（低 QPS → 高 QPS → 低 QPS）。
- [ ] 记录指标：
  - 吞吐量、平均延迟、P99 延迟（随时间变化）。
  - 当前使用的共识协议（记录切换时间点）。
  - （可选）切换期间的性能抖动。

### 数据分析与对比
- [ ] 绘制图表：
  - 基准测试：每种协议的吞吐量-QPS 曲线、延迟-QPS 曲线。
  - 动态负载测试：吞吐量/延迟随时间变化的曲线，标记切换时间点。
- [ ] 对比：
  - 动态负载测试中切换层在特定负载阶段的性能与最佳单一协议的基准测试结果。
  - 整个动态负载场景下，切换层的平均/总吞吐量和平均延迟与固定单一协议的性能。
- [ ] 分析：
  - 解释切换层为何以及何时进行切换。
  - 分析切换带来的性能提升或可能的开销。
  - 讨论当前切换策略 (`pickCp`) 的有效性和局限性。

## 代码修改
### 监控指标收集
- [ ] 在 `server` 中添加字段存储监控指标（如近期请求延迟、吞吐量、冲突率估计、读写比例）。
- [ ] 实现指标收集逻辑（例如，在 `handleClientRequest` 中记录请求延迟）。

### 切换决策逻辑
- [ ] 实现 `decideNextProtocol` 函数，基于监控指标选择最佳协议。
- [ ] 修改 `handleClientRequest`，定期调用 `decideNextProtocol` 并更新当前协议。

### 实验支持
- [ ] 添加日志记录，便于分析切换行为和性能。
- [ ] 实现实验控制接口（例如，通过命令行参数启用/禁用切换层，设置负载场景）。

## 时间安排
- **第 1 周**：完成实验环境搭建和负载生成器开发。
- **第 2 周**：实现监控指标收集和切换决策逻辑，完成代码修改。
- **第 3 周**：运行基准测试，收集数据。
- **第 4 周**：运行动态负载测试，收集数据。
- **第 5 周**：数据分析与对比，撰写实验报告。

## 备注
- 实验过程中需记录详细的配置和运行参数，确保结果可复现。
- 数据分析时需注意统计显著性，必要时进行多次实验取平均值。
