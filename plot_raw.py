import pandas as pd
import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
latency_file = r'C:\Users\<USER>\Desktop\CPOrchestra\latency'
all_latencies = pd.read_csv(latency_file, header=None, names=['Latency(ms)']).iloc[:80000]

num_points = len(all_latencies)
if num_points < 80000:
    print(f"警告：数据点不足80000，只有 {num_points} 个点。")

# 创建序列号
seq_nos = np.arange(num_points)

# --- 全局异常值过滤 (用于平滑曲线) ---
Q1_all = all_latencies['Latency(ms)'].quantile(0.25)
Q3_all = all_latencies['Latency(ms)'].quantile(0.75)
IQR_all = Q3_all - Q1_all
lower_bound_all = Q1_all - 1.5 * IQR_all
upper_bound_all = Q3_all + 1.5 * IQR_all

# 创建布尔掩码，标记哪些点是有效的（非异常值）
mask_all = (all_latencies['Latency(ms)'] >= lower_bound_all) & (all_latencies['Latency(ms)'] <= upper_bound_all)

# 获取过滤后的数据和对应的序列号
filtered_latencies_global = all_latencies['Latency(ms)'][mask_all]
filtered_seq_nos_global = seq_nos[mask_all]

# --- 创建基于全局过滤后数据的移动平均 ---
window_size = 100
# 注意：直接在过滤后的Series上应用rolling可能因索引不连续导致问题或警告
# 更好的方法是创建一个包含NaN的新Series，然后应用rolling
latencies_for_smoothing = all_latencies['Latency(ms)'].copy()
latencies_for_smoothing[~mask_all] = np.nan # 将异常值替换为NaN
filtered_latencies_smooth = latencies_for_smoothing.rolling(window=window_size, center=True, min_periods=1).mean()
# 绘制时仍然使用原始的seq_nos，因为NaN会被忽略

# 创建图表
plt.figure(figsize=(18, 10))

# --- 绘制数据 ---
# 绘制原始数据（半透明） - 包含异常值
plt.plot(filtered_seq_nos_global, filtered_latencies_global, alpha=1, color='blue', label='原始数据')

# 绘制基于过滤后数据的平滑曲线
# 使用原始seq_nos和包含NaN的平滑数据，matplotlib会跳过NaN点
# plt.plot(seq_nos, filtered_latencies_smooth, label='延迟 (移动平均 - 基于过滤后数据)', linewidth=2, color='blue')

# --- 分段统计和标注 (逻辑不变，仍在段内过滤计算统计值) ---
segment_size = 20000
protocols = ['Paxos', 'EPaxos', 'M2Paxos', 'VPaxos']
num_segments = (num_points + segment_size - 1) // segment_size

# 获取Y轴范围并调整
# 需要在绘制曲线后获取ylim
y_min, y_max = plt.ylim()
text_y_position = y_max * 1.05
plt.ylim(y_min, text_y_position * 1.05)

for i in range(num_segments):
    start_idx = i * segment_size
    end_idx = min((i + 1) * segment_size, num_points)
    segment_latencies = all_latencies['Latency(ms)'][start_idx:end_idx]
    original_count = len(segment_latencies)

    if original_count > 0:
        # 段内异常值过滤 (仅用于统计)
        Q1_seg = segment_latencies.quantile(0.25)
        Q3_seg = segment_latencies.quantile(0.75)
        IQR_seg = Q3_seg - Q1_seg
        lower_bound_seg = Q1_seg - 1.5 * IQR_seg
        upper_bound_seg = Q3_seg + 1.5 * IQR_seg
        filtered_segment_latencies = segment_latencies[(segment_latencies >= lower_bound_seg) & (segment_latencies <= upper_bound_seg)]
        filtered_count = len(filtered_segment_latencies)
        
        # 基于段内过滤后数据计算统计信息
        if filtered_count > 0:
             p50 = np.percentile(filtered_segment_latencies, 50)
             p95 = np.percentile(filtered_segment_latencies, 95)
             p99 = np.percentile(filtered_segment_latencies, 99)
        else:
            p50, p95, p99 = np.nan, np.nan, np.nan

        # 标注 (内容不变)
        text_x_position = start_idx + (end_idx - start_idx) / 2
        protocol_name = protocols[i] if i < len(protocols) else f'段 {i+1}'
        stats_text = (f"{protocol_name}\n")
                    #   f"(段内保留点: {filtered_count}/{original_count})\n" # 强调是段内过滤
                    #   f"P50: {p50:.2f}\n"
                    #   f"P95: {p95:.2f}\n"
                    #   f"P99: {p99:.2f}")

        plt.text(text_x_position, text_y_position, stats_text,
                 fontsize=9, verticalalignment='bottom', horizontalalignment='center',
                 bbox=dict(facecolor='white', alpha=0.7, boxstyle='round,pad=0.3'))

        # 垂直分割线
        if i > 0:
            plt.axvline(start_idx, color='black', linestyle=':', linewidth=1, alpha=0.5)

# --- 图表收尾 ---
# plt.title(f'请求延迟分析（移动平均基于全局过滤数据，分段统计基于段内过滤）') # 更新标题
plt.xlabel('请求序列号')
plt.ylabel('延迟 (ms)')
plt.legend(loc='upper left')
plt.grid(True)

# 保存图表
plt.savefig(f'latency_analysis_{num_points}_segmented_filtered_curve.png') # 更新文件名
plt.show()

print(f"图表已保存为 latency_analysis_{num_points}_segmented_filtered_curve.png")