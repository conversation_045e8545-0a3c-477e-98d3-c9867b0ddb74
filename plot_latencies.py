import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import math
import seaborn as sns # Import Seaborn

# 设置中文字体，确保图表能正确显示中文
plt.rcParams['font.sans-serif'] = ['SimHei']  # 或者 'Microsoft YaHei' 等其他支持中文的字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号'-'显示为方块的问题
name = r"history"
# --- Configuration ---
latency_file = r'./'+name+r'.csv' # 请确保这是你的CSV文件路径
segment_duration_seconds = 30 # Segment duration in seconds
max_plot_time_seconds = 120  # Maximum time (in seconds from start) to include in the plot
protocols = ['Paxos', 'EPaxos', 'M2Paxos', 'VPaxos'] # 按顺序对应的协议名称
aggregation_interval = 0.5 # Keep for the fine-grained line visualization

# --- Seaborn Styling ---
# Apply a Seaborn theme for better aesthetics.
# Common styles: "darkgrid", "whitegrid", "dark", "white", "ticks"
# Contexts adjust element sizes: "paper", "notebook", "talk", "poster"
sns.set_theme(style="whitegrid", context="paper",font="SimHei") # Using whitegrid style and 'talk' context for larger elements

# --- Data Loading and Cleaning ---
try:
    df = pd.read_csv(latency_file, header=None, usecols=[2, 3], names=['StartTime', 'EndTime'])
    original_rows_read = len(df)
    print(f"读取的数据行数: {original_rows_read}")

    df['StartTime'] = pd.to_numeric(df['StartTime'], errors='coerce')
    df['EndTime'] = pd.to_numeric(df['EndTime'], errors='coerce')
    df_valid_times = df.dropna(subset=['StartTime', 'EndTime']).copy()
    print(f"删除无效时间后行数: {len(df_valid_times)}")

    df_cleaned = df_valid_times[df_valid_times['EndTime'] >= df_valid_times['StartTime']].copy()
    valid_rows = len(df_cleaned)
    print(f"过滤无效时间差后行数: {valid_rows}")

    if valid_rows == 0:
        print("错误：没有有效的延迟数据可供处理。")
        exit()

    # --- Data Preparation & Filtering by Time ---
    min_time = df_cleaned['StartTime'].min()
    print(f"数据起始时间: {min_time:.2f}s")

    absolute_cutoff_time = min_time + max_plot_time_seconds
    df_filtered = df_cleaned[df_cleaned['StartTime'] < absolute_cutoff_time].copy()
    print(f"过滤到 {max_plot_time_seconds} 秒后行数 (最终用于绘图): {len(df_filtered)}")

    if df_filtered.empty:
        print(f"错误：在 {max_plot_time_seconds} 秒截止时间前没有有效的延迟数据。")
        exit()

    max_time_filtered = df_filtered['StartTime'].max()
    print(f"过滤后数据时间范围: {min_time:.2f}s to {max_time_filtered:.2f}s")

    df_filtered['Latency(ms)'] = (df_filtered['EndTime'] - df_filtered['StartTime']) * 1000
    df_filtered['FineTimeInterval'] = ((df_filtered['StartTime'] - min_time) / aggregation_interval).astype(int)
    df_filtered['SegmentID'] = ((df_filtered['StartTime'] - min_time) // segment_duration_seconds).astype(int)
    max_segment_id_in_data = df_filtered['SegmentID'].max() if not df_filtered.empty else -1
    print(f"过滤后数据中最大SegmentID: {max_segment_id_in_data}")

except FileNotFoundError:
    print(f"错误：文件未找到 {latency_file}")
    exit()
except ValueError as e:
    print(f"读取CSV时出错，请检查文件格式是否正确: {e}")
    exit()
except Exception as e:
    print(f"处理文件时发生未知错误: {e}")
    exit()

# --- Fine-grained Aggregation ---
aggregated_latency_fine = df_filtered.groupby('FineTimeInterval')['Latency(ms)'].mean()

# --- Segment Aggregation ---
def p95(x):
    return x.quantile(0.95)

def p99(x):
    return x.quantile(0.99)

segment_stats = df_filtered.groupby('SegmentID')['Latency(ms)'].agg(['mean', p95, p99])
segment_stats.rename(columns={'mean': 'Avg', 'p95': 'P95', 'p99': 'P99'}, inplace=True)

print("\n--- 每 30 秒段统计 (仅限前 {} 秒数据) ---".format(max_plot_time_seconds))
print(segment_stats)

if segment_stats.empty and aggregated_latency_fine.empty:
    print("错误：聚合后没有数据可供绘制。")
    exit()

# --- Plotting ---
plt.figure(figsize=(18, 10)) # Seaborn theme affects elements inside the figure
plot_end_time = absolute_cutoff_time

# --- Plot Fine-grained Aggregated Line ---
if not aggregated_latency_fine.empty:
    plot_times_fine = min_time + aggregated_latency_fine.index * aggregation_interval
    # Use a slightly different color that might fit better with Seaborn themes
    plt.plot(plot_times_fine, aggregated_latency_fine.values, label=f'延迟 (每 {aggregation_interval} 秒聚合)', color='steelblue', linewidth=1.5, alpha=0.8) # Adjusted color/linewidth


# --- Draw Vertical Lines and Add Labels/Stats ---
# Calculate Y position relative to data plotted so far
temp_y_min, temp_y_max = plt.ylim()
if not aggregated_latency_fine.empty:
    data_max_y = max(temp_y_max, aggregated_latency_fine.max() if not aggregated_latency_fine.empty else 0) # Consider aggregated line max
else:
    # Estimate max Y from segment stats if fine line isn't plotted
    data_max_y = max(temp_y_max, segment_stats['P99'].max() if not segment_stats.empty else 0)

# Set bottom limit firmly to 0 or slightly below if needed
plt.ylim(bottom=0)
y_min_final, _ = plt.ylim() # Get the actual bottom limit (likely 0)

# Recalculate positions based on actual data range and ensuring space above
y_max_for_text = data_max_y * 1.05 # Add 5% buffer above highest data point
protocol_text_y = y_max_for_text * 1.03 # Position protocol text slightly above the buffer
stats_text_y_base = y_max_for_text     # Position stats text starting at the buffer line

# Set final Y limit to accommodate text comfortably
plt.ylim(y_min_final, protocol_text_y * 1.08) # Extend Y range slightly more

num_segments_in_plot_range = math.ceil(max_plot_time_seconds / segment_duration_seconds)
print(f"将绘制最多 {num_segments_in_plot_range} 个时间段的标记 (直到 {max_plot_time_seconds} 秒)。")

for i in range(num_segments_in_plot_range):
    segment_start_time = min_time + i * segment_duration_seconds
    segment_end_time = min_time + (i + 1) * segment_duration_seconds

    if segment_start_time >= plot_end_time:
        continue

    line_time = min(segment_end_time, plot_end_time)
    if line_time > min_time and line_time <= plot_end_time :
         # Use a slightly less prominent color/style for the vertical line with Seaborn themes
         plt.axvline(line_time, color='grey', linestyle='--', linewidth=1.5, alpha=0.7) # Adjusted style

    effective_segment_end = min(segment_end_time, plot_end_time)
    label_x_position = (segment_start_time + effective_segment_end) / 2

    # Add Protocol Label
    if i < len(protocols) and i in segment_stats.index:
        protocol_name = protocols[i]
        plt.text(label_x_position, protocol_text_y, protocol_name,
                 # Increase font size slightly due to 'talk' context
                 fontsize=16, fontweight='bold', verticalalignment='bottom', horizontalalignment='center',
                 bbox=dict(facecolor='white', alpha=0.85, boxstyle='round,pad=0.4', edgecolor='lightgrey')) # Enhanced bbox

    # Add Segment Stats Text
    if i in segment_stats.index:
        stats = segment_stats.loc[i]
        stats_text = f"Avg: {stats['Avg']:.2f} ms\nP95: {stats['P95']:.2f} ms\nP99: {stats['P99']:.2f} ms"
        # Slightly smaller font size for stats compared to protocol name
        plt.text(label_x_position, stats_text_y_base, stats_text,
                 fontsize=11, verticalalignment='top', horizontalalignment='center',
                 bbox=dict(facecolor='white', alpha=0.75, boxstyle='round,pad=0.3', edgecolor='lightgrey')) # Enhanced bbox


# --- Final Chart Adjustments ---
# Use a slightly larger font for title/labels consistent with 'talk' context
plt.title(f'请求延迟', fontsize=20)
plt.xlabel('时间 (秒)', fontsize=14)
plt.ylabel('延迟 (ms)', fontsize=14)

plt.xlim(min_time, plot_end_time)

# Adjust legend appearance
handles, labels = plt.gca().get_legend_handles_labels()
if handles:
    plt.legend(loc='upper left', frameon=True, shadow=True, fontsize=12) # Add frame/shadow

# Grid is handled by sns.set_theme(style="whitegrid"), no need for plt.grid()
# plt.grid(True, which='both', linestyle='--', linewidth=0.5) # Remove or comment out

plt.tight_layout(rect=[0, 0, 1, 0.95]) # Adjust rect to prevent title overlap

# Save the chart
save_filename = f'1500qps_100c_0.5w_3.png' # Updated filename
plt.savefig(save_filename)
plt.show()

print(f"\n图表已保存为 {save_filename}")

# Optional: Reset to default Matplotlib style if you have more plots later
# plt.style.use('default')
# plt.rcParams.update(plt.rcParamsDefault)
# # Re-apply Chinese font settings if needed after reset
# plt.rcParams['font.sans-serif'] = ['SimHei']
# plt.rcParams['axes.unicode_minus'] = False