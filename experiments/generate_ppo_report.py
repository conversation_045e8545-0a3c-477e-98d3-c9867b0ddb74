#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PPO强化学习协议选择实验结果分析和可视化报告生成器
"""

import os
import sys
import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from pathlib import Path
from typing import Dict, List, Any
import argparse

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PPOReportGenerator:
    def __init__(self, results_dir: str):
        self.results_dir = Path(results_dir)
        self.summary_file = self.results_dir / "experiment_summary.csv"
        self.output_dir = self.results_dir / "reports"
        self.output_dir.mkdir(exist_ok=True)
        
    def load_data(self) -> pd.DataFrame:
        """加载实验结果数据"""
        if not self.summary_file.exists():
            raise FileNotFoundError(f"未找到汇总文件: {self.summary_file}")
        
        df = pd.read_csv(self.summary_file)
        # 转换数值列
        numeric_cols = ['平均延迟(ms)', '吞吐量(ops/s)', 'P99延迟(ms)', '标准差']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        return df
    
    def analyze_ppo_performance(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析PPO性能提升"""
        results = {}
        
        # 按工作负载分组分析
        for workload in df['工作负载'].unique():
            workload_data = df[df['工作负载'] == workload]
            
            # 获取PPO结果
            ppo_data = workload_data[workload_data['实验类型'] == 'PPO自适应']
            if ppo_data.empty:
                continue
                
            ppo_latency = ppo_data['平均延迟(ms)'].iloc[0]
            ppo_throughput = ppo_data['吞吐量(ops/s)'].iloc[0]
            
            # 获取基准实验中的最佳结果
            baseline_data = workload_data[workload_data['实验类型'] == '基准实验']
            if baseline_data.empty:
                continue
                
            best_baseline_latency = baseline_data['平均延迟(ms)'].min()
            best_baseline_throughput = baseline_data['吞吐量(ops/s)'].max()
            
            # 计算改进百分比
            latency_improvement = ((best_baseline_latency - ppo_latency) / best_baseline_latency) * 100
            throughput_improvement = ((ppo_throughput - best_baseline_throughput) / best_baseline_throughput) * 100
            
            results[workload] = {
                'ppo_latency': ppo_latency,
                'ppo_throughput': ppo_throughput,
                'best_baseline_latency': best_baseline_latency,
                'best_baseline_throughput': best_baseline_throughput,
                'latency_improvement_pct': latency_improvement,
                'throughput_improvement_pct': throughput_improvement
            }
        
        return results
    
    def create_performance_comparison_chart(self, df: pd.DataFrame):
        """创建性能对比图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 延迟对比
        workloads = df['工作负载'].unique()
        ppo_latencies = []
        best_baseline_latencies = []
        
        for workload in workloads:
            workload_data = df[df['工作负载'] == workload]
            
            ppo_data = workload_data[workload_data['实验类型'] == 'PPO自适应']
            if not ppo_data.empty:
                ppo_latencies.append(ppo_data['平均延迟(ms)'].iloc[0])
            else:
                ppo_latencies.append(np.nan)
            
            baseline_data = workload_data[workload_data['实验类型'] == '基准实验']
            if not baseline_data.empty:
                best_baseline_latencies.append(baseline_data['平均延迟(ms)'].min())
            else:
                best_baseline_latencies.append(np.nan)
        
        x = np.arange(len(workloads))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, best_baseline_latencies, width, label='最佳基准协议', alpha=0.8)
        bars2 = ax1.bar(x + width/2, ppo_latencies, width, label='PPO自适应', alpha=0.8)
        
        ax1.set_ylabel('平均延迟 (ms)')
        ax1.set_title('延迟性能对比')
        ax1.set_xticks(x)
        ax1.set_xticklabels(workloads, rotation=45)
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 吞吐量对比
        ppo_throughputs = []
        best_baseline_throughputs = []
        
        for workload in workloads:
            workload_data = df[df['工作负载'] == workload]
            
            ppo_data = workload_data[workload_data['实验类型'] == 'PPO自适应']
            if not ppo_data.empty:
                ppo_throughputs.append(ppo_data['吞吐量(ops/s)'].iloc[0])
            else:
                ppo_throughputs.append(np.nan)
            
            baseline_data = workload_data[workload_data['实验类型'] == '基准实验']
            if not baseline_data.empty:
                best_baseline_throughputs.append(baseline_data['吞吐量(ops/s)'].max())
            else:
                best_baseline_throughputs.append(np.nan)
        
        bars3 = ax2.bar(x - width/2, best_baseline_throughputs, width, label='最佳基准协议', alpha=0.8)
        bars4 = ax2.bar(x + width/2, ppo_throughputs, width, label='PPO自适应', alpha=0.8)
        
        ax2.set_ylabel('吞吐量 (ops/s)')
        ax2.set_title('吞吐量性能对比')
        ax2.set_xticks(x)
        ax2.set_xticklabels(workloads, rotation=45)
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def create_improvement_chart(self, analysis_results: Dict[str, Any]):
        """创建改进百分比图"""
        workloads = list(analysis_results.keys())
        latency_improvements = [analysis_results[w]['latency_improvement_pct'] for w in workloads]
        throughput_improvements = [analysis_results[w]['throughput_improvement_pct'] for w in workloads]
        
        fig, ax = plt.subplots(figsize=(12, 6))
        
        x = np.arange(len(workloads))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, latency_improvements, width, label='延迟改进 (%)', alpha=0.8, color='green')
        bars2 = ax.bar(x + width/2, throughput_improvements, width, label='吞吐量改进 (%)', alpha=0.8, color='blue')
        
        ax.set_ylabel('改进百分比 (%)')
        ax.set_title('PPO相对于最佳基准协议的性能改进')
        ax.set_xticks(x)
        ax.set_xticklabels(workloads, rotation=45)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='red', linestyle='--', alpha=0.5)
        
        # 在柱状图上添加数值标签
        for bar, value in zip(bars1, latency_improvements):
            if not np.isnan(value):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                       f'{value:.1f}%', ha='center', va='bottom')
        
        for bar, value in zip(bars2, throughput_improvements):
            if not np.isnan(value):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5,
                       f'{value:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'improvement_percentage.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def create_protocol_distribution_chart(self, df: pd.DataFrame):
        """创建协议选择分布图"""
        baseline_data = df[df['实验类型'] == '基准实验']
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        axes = axes.flatten()
        
        workloads = df['工作负载'].unique()
        
        for i, workload in enumerate(workloads):
            if i >= len(axes):
                break
                
            workload_baseline = baseline_data[baseline_data['工作负载'] == workload]
            
            if not workload_baseline.empty:
                protocols = workload_baseline['协议'].tolist()
                latencies = workload_baseline['平均延迟(ms)'].tolist()
                
                # 创建协议性能条形图
                bars = axes[i].bar(protocols, latencies, alpha=0.7)
                axes[i].set_title(f'{workload}工作负载下的协议性能')
                axes[i].set_ylabel('平均延迟 (ms)')
                axes[i].tick_params(axis='x', rotation=45)
                axes[i].grid(True, alpha=0.3)
                
                # 高亮最佳协议
                best_idx = np.argmin(latencies)
                bars[best_idx].set_color('orange')
                bars[best_idx].set_alpha(1.0)
        
        # 隐藏未使用的子图
        for i in range(len(workloads), len(axes)):
            axes[i].set_visible(False)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'protocol_distribution.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    def generate_html_report(self, df: pd.DataFrame, analysis_results: Dict[str, Any]):
        """生成HTML格式的实验报告"""
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPO强化学习协议选择实验报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        .header {{ background-color: #f4f4f4; padding: 20px; border-radius: 5px; }}
        .section {{ margin: 20px 0; }}
        .chart {{ text-align: center; margin: 20px 0; }}
        .chart img {{ max-width: 100%; height: auto; }}
        table {{ border-collapse: collapse; width: 100%; margin: 20px 0; }}
        th, td {{ border: 1px solid #ddd; padding: 12px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        .improvement {{ color: green; font-weight: bold; }}
        .degradation {{ color: red; font-weight: bold; }}
        .summary-box {{ background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>PPO强化学习协议选择实验报告</h1>
        <p>生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="section">
        <h2>实验概述</h2>
        <p>本报告展示了PPO（Proximal Policy Optimization）强化学习算法在分布式一致性协议自适应选择中的实验结果。
        实验对比了PPO自适应协议选择与固定协议的性能表现。</p>
        
        <div class="summary-box">
            <h3>实验配置</h3>
            <ul>
                <li>测试协议: Paxos, EPaxos, M2Paxos, VPaxos</li>
                <li>工作负载类型: {len(df['工作负载'].unique())}种</li>
                <li>实验时长: 每个场景5分钟</li>
                <li>PPO切换间隔: 60秒</li>
            </ul>
        </div>
    </div>
    
    <div class="section">
        <h2>性能对比结果</h2>
        <div class="chart">
            <img src="performance_comparison.png" alt="性能对比图">
        </div>
    </div>
    
    <div class="section">
        <h2>PPO改进效果</h2>
        <div class="chart">
            <img src="improvement_percentage.png" alt="改进百分比图">
        </div>
        
        <h3>详细改进分析</h3>
        <table>
            <tr>
                <th>工作负载</th>
                <th>PPO延迟 (ms)</th>
                <th>最佳基准延迟 (ms)</th>
                <th>延迟改进</th>
                <th>PPO吞吐量 (ops/s)</th>
                <th>最佳基准吞吐量 (ops/s)</th>
                <th>吞吐量改进</th>
            </tr>
        """
        
        for workload, results in analysis_results.items():
            latency_class = "improvement" if results['latency_improvement_pct'] > 0 else "degradation"
            throughput_class = "improvement" if results['throughput_improvement_pct'] > 0 else "degradation"
            
            html_content += f"""
            <tr>
                <td>{workload}</td>
                <td>{results['ppo_latency']:.2f}</td>
                <td>{results['best_baseline_latency']:.2f}</td>
                <td class="{latency_class}">{results['latency_improvement_pct']:.1f}%</td>
                <td>{results['ppo_throughput']:.0f}</td>
                <td>{results['best_baseline_throughput']:.0f}</td>
                <td class="{throughput_class}">{results['throughput_improvement_pct']:.1f}%</td>
            </tr>
            """
        
        html_content += """
        </table>
    </div>
    
    <div class="section">
        <h2>协议性能分布</h2>
        <div class="chart">
            <img src="protocol_distribution.png" alt="协议性能分布图">
        </div>
    </div>
    
    <div class="section">
        <h2>完整数据表</h2>
        """
        
        # 添加完整数据表
        html_content += df.to_html(classes='data-table', table_id='full-data', escape=False)
        
        html_content += """
    </div>
    
    <div class="section">
        <h2>结论与建议</h2>
        <div class="summary-box">
            <h3>主要发现</h3>
            <ul>
        """
        
        # 生成结论
        total_improvements = sum(1 for r in analysis_results.values() if r['latency_improvement_pct'] > 0)
        total_workloads = len(analysis_results)
        
        html_content += f"""
                <li>PPO算法在{total_improvements}/{total_workloads}种工作负载下实现了延迟改进</li>
        """
        
        avg_latency_improvement = np.mean([r['latency_improvement_pct'] for r in analysis_results.values()])
        avg_throughput_improvement = np.mean([r['throughput_improvement_pct'] for r in analysis_results.values()])
        
        html_content += f"""
                <li>平均延迟改进: {avg_latency_improvement:.1f}%</li>
                <li>平均吞吐量改进: {avg_throughput_improvement:.1f}%</li>
            </ul>
            
            <h3>技术建议</h3>
            <ul>
                <li>PPO算法展现了在动态工作负载下的协议选择优势</li>
                <li>建议在生产环境中使用更长的观察窗口以提高决策质量</li>
                <li>可以考虑结合更多的性能指标来改进奖励函数设计</li>
            </ul>
        </div>
    </div>
</body>
</html>
        """
        
        # 保存HTML报告
        report_file = self.output_dir / 'ppo_experiment_report.html'
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return report_file
    
    def generate_report(self):
        """生成完整的实验报告"""
        print("加载实验数据...")
        df = self.load_data()
        
        print("分析PPO性能...")
        analysis_results = self.analyze_ppo_performance(df)
        
        print("生成性能对比图...")
        self.create_performance_comparison_chart(df)
        
        print("生成改进效果图...")
        self.create_improvement_chart(analysis_results)
        
        print("生成协议分布图...")
        self.create_protocol_distribution_chart(df)
        
        print("生成HTML报告...")
        report_file = self.generate_html_report(df, analysis_results)
        
        print(f"实验报告已生成:")
        print(f"  - HTML报告: {report_file}")
        print(f"  - 图表目录: {self.output_dir}")
        
        return report_file

def main():
    parser = argparse.ArgumentParser(description='生成PPO实验报告')
    parser.add_argument('results_dir', help='实验结果目录')
    
    args = parser.parse_args()
    
    try:
        generator = PPOReportGenerator(args.results_dir)
        generator.generate_report()
    except Exception as e:
        print(f"生成报告时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()