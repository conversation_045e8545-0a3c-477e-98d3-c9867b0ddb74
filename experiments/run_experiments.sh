#!/bin/bash
# PPO共识协议切换性能评估实验运行脚本

set -e

# 配置参数
EXPERIMENT_DIR="./experiment_results"
BIN_DIR="./bin"
CONFIG_FILE="./experiments/experiment_config.json"
LOG_LEVEL="info"

# 创建实验目录
mkdir -p "$EXPERIMENT_DIR"

echo "=== PPO共识协议切换性能评估实验 ==="
echo "实验结果将保存到: $EXPERIMENT_DIR"

# 1. 构建项目
echo "1. 构建项目..."
cd bin && ./build.sh && cd ..

# 2. 运行基准测试
echo "2. 运行基准协议性能测试..."
for protocol in "paxos" "epaxos" "m2paxos" "vpaxos"; do
    echo "  测试协议: $protocol"
    
    for scenario in "read_intensive" "write_intensive" "mixed_workload" "low_conflict" "high_conflict"; do
        echo "    场景: $scenario"
        
        # 启动服务器
        PROTOCOL=$protocol SCENARIO=$scenario LOG_LEVEL=$LOG_LEVEL \
        go run ./experiments/experiment_runner.go \
        --mode=baseline \
        --protocol=$protocol \
        --scenario=$scenario \
        --duration=300 \
        --output="$EXPERIMENT_DIR/baseline_${protocol}_${scenario}.json" &
        
        SERVER_PID=$!
        sleep 5
        
        # 运行客户端
        SCENARIO=$scenario go run ./experiments/experiment_runner.go \
        --mode=client \
        --scenario=$scenario \
        --duration=300 &
        
        CLIENT_PID=$!
        
        # 等待实验完成
        wait $CLIENT_PID
        wait $SERVER_PID
        
        echo "    完成: $scenario"
        sleep 10
    done
done

# 3. 运行PPO动态切换测试
echo "3. 运行PPO动态协议切换测试..."
for scenario in "dynamic_workload" "adaptive_mixed" "varying_conflict"; do
    echo "  PPO场景: $scenario"
    
    # 启动PPO服务器
    PPO_ENABLED=true SCENARIO=$scenario LOG_LEVEL=$LOG_LEVEL \
    go run ./experiments/experiment_runner.go \
    --mode=ppo \
    --scenario=$scenario \
    --duration=900 \
    --output="$EXPERIMENT_DIR/ppo_${scenario}.json" &
    
    SERVER_PID=$!
    sleep 5
    
    # 运行动态负载客户端
    SCENARIO=$scenario go run ./experiments/experiment_runner.go \
    --mode=client \
    --scenario=$scenario \
    --duration=900 &
    
    CLIENT_PID=$!
    
    # 等待实验完成
    wait $CLIENT_PID
    wait $SERVER_PID
    
    echo "  完成: $scenario"
    sleep 15
done

# 4. 运行消融实验
echo "4. 运行PPO消融实验..."
for config in "no_entropy" "no_switch_penalty" "high_lr" "low_lr"; do
    echo "  消融配置: $config"
    
    # 启动配置化PPO服务器
    PPO_ENABLED=true PPO_CONFIG=$config SCENARIO="mixed_workload" LOG_LEVEL=$LOG_LEVEL \
    go run ./experiments/experiment_runner.go \
    --mode=ppo \
    --scenario=mixed_workload \
    --duration=600 \
    --output="$EXPERIMENT_DIR/ablation_${config}.json" &
    
    SERVER_PID=$!
    sleep 5
    
    # 运行客户端
    SCENARIO="mixed_workload" go run ./experiments/experiment_runner.go \
    --mode=client \
    --scenario=mixed_workload \
    --duration=600 &
    
    CLIENT_PID=$!
    
    # 等待实验完成
    wait $CLIENT_PID
    wait $SERVER_PID
    
    echo "  完成: $config"
    sleep 10
done

# 5. 生成结果分析
echo "5. 生成实验结果分析..."
python3 ./experiments/visualize_results.py --data-dir="$EXPERIMENT_DIR"

# 6. 生成实验报告
echo "6. 生成实验报告..."
python3 ./experiments/generate_report.py --data-dir="$EXPERIMENT_DIR" --output="$EXPERIMENT_DIR/experiment_report.md"

echo "=== 实验完成 ==="
echo "结果文件:"
echo "  - 原始数据: $EXPERIMENT_DIR/*.json"
echo "  - 图表: $EXPERIMENT_DIR/figures/*.png"
echo "  - 报告: $EXPERIMENT_DIR/experiment_report.md"
echo "  - 汇总表: $EXPERIMENT_DIR/figures/performance_summary.csv"