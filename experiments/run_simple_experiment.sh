#!/bin/bash

# 简化的PPO实验脚本
# 使用原始客户端但通过日志处理性能指标

set -e

EXPERIMENT_DIR="$(dirname "$0")"
RESULTS_DIR="$EXPERIMENT_DIR/results"
LOGS_DIR="$EXPERIMENT_DIR/logs"
DURATION=60  # 缩短到1分钟用于测试
WARMUP_TIME=10

mkdir -p "$RESULTS_DIR" "$LOGS_DIR"

echo "=== 简化PPO实验 ==="

# 工作负载列表
WORKLOADS=("mixed_read_write" "read_heavy" "write_heavy")
PROTOCOLS=("paxos" "epaxos")

# 启动集群函数
start_cluster() {
    echo "启动服务器集群..."
    pkill -f "paxi.*server" || true
    sleep 2
    
    for i in {1..3}; do
        nohup ../bin/server -id=1.$i -config="../bin/config.json" > "$LOGS_DIR/server_$i.log" 2>&1 &
        echo "启动服务器 1.$i"
    done
    sleep 5
}

# 停止集群函数
stop_cluster() {
    echo "停止服务器集群..."
    pkill -f "paxi.*server" || true
    sleep 2
}

# 运行基准测试
run_baseline() {
    local protocol=$1
    local workload=$2
    local output_file=$3
    
    echo "运行基准实验: $protocol + $workload"
    
    export PROTOCOL_PICKER="fixed"
    export FIXED_PROTOCOL="$protocol"
    export WORKLOAD_TYPE="$workload"
    
    start_cluster
    
    # 运行测试并捕获日志
    echo "运行性能测试..."
    timeout $DURATION ../bin/client -config="../bin/config.json" > "$LOGS_DIR/test_output.log" 2>&1 || true
    
    # 从日志中提取性能数据并生成JSON
    python3 << EOF
import json
import re
import os

# 读取日志文件
log_file = "$LOGS_DIR/test_output.log"
output_file = "$output_file"

try:
    with open(log_file, 'r') as f:
        content = f.read()
    
    # 提取性能指标（使用正则表达式）
    throughput_match = re.search(r'Throughput = ([0-9.]+)', content)
    latency_match = re.search(r'mean = ([0-9.]+)', content)
    p99_match = re.search(r'p99 = ([0-9.]+)', content)
    
    throughput = float(throughput_match.group(1)) if throughput_match else 0.0
    avg_latency = float(latency_match.group(1)) if latency_match else 0.0
    p99_latency = float(p99_match.group(1)) if p99_match else 0.0
    
    # 生成JSON输出
    metrics = {
        "experiment_type": "baseline",
        "workload_type": "$workload",
        "protocol": "$protocol",
        "average_latency_ms": avg_latency,
        "throughput_ops_per_sec": throughput,
        "p99_latency_ms": p99_latency,
        "latency_std_dev_ms": 0.0,
        "total_operations": int(throughput * $DURATION),
        "duration_seconds": $DURATION,
        "timestamp": "$(date -Iseconds)"
    }
    
    with open(output_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    
    print(f"Metrics saved to {output_file}")
    
except Exception as e:
    print(f"Error processing logs: {e}")
    # 创建空的结果文件
    with open(output_file, 'w') as f:
        json.dump({"error": str(e)}, f)
EOF
    
    stop_cluster
}

# 主实验循环
main() {
    echo "开始简化实验..."
    
    SUMMARY_FILE="$RESULTS_DIR/experiment_summary.csv"
    echo "实验类型,工作负载,协议,平均延迟(ms),吞吐量(ops/s),P99延迟(ms)" > "$SUMMARY_FILE"
    
    for workload in "${WORKLOADS[@]}"; do
        echo ""
        echo "=== 工作负载: $workload ==="
        
        for protocol in "${PROTOCOLS[@]}"; do
            baseline_output="$RESULTS_DIR/baseline_${workload}_${protocol}.json"
            run_baseline "$protocol" "$workload" "$baseline_output"
            
            # 添加到汇总文件
            if [ -f "$baseline_output" ]; then
                avg_latency=$(python3 -c "import json; print(json.load(open('$baseline_output')).get('average_latency_ms', 'N/A'))" 2>/dev/null || echo "N/A")
                throughput=$(python3 -c "import json; print(json.load(open('$baseline_output')).get('throughput_ops_per_sec', 'N/A'))" 2>/dev/null || echo "N/A")
                p99_latency=$(python3 -c "import json; print(json.load(open('$baseline_output')).get('p99_latency_ms', 'N/A'))" 2>/dev/null || echo "N/A")
                
                echo "基准实验,$workload,$protocol,$avg_latency,$throughput,$p99_latency" >> "$SUMMARY_FILE"
            fi
        done
    done
    
    echo ""
    echo "=== 实验完成 ==="
    echo "结果保存在: $RESULTS_DIR"
    echo "汇总文件: $SUMMARY_FILE"
}

# 清理函数
cleanup() {
    echo "清理实验环境..."
    stop_cluster
    unset PROTOCOL_PICKER FIXED_PROTOCOL WORKLOAD_TYPE
}

trap cleanup EXIT INT TERM

main "$@"
