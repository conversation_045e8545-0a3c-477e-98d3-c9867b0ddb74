#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的实验结果分析和可视化
"""

import os
import sys
import json
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def load_json_results(results_dir):
    """加载所有JSON结果文件"""
    results = []
    results_path = Path(results_dir)
    
    for json_file in results_path.glob("*.json"):
        if json_file.name.startswith(("baseline_", "ppo_")):
            try:
                with open(json_file) as f:
                    data = json.load(f)
                    if "error" not in data:  # 跳过错误文件
                        results.append(data)
                        print(f"加载了结果文件: {json_file.name}")
            except Exception as e:
                print(f"无法加载 {json_file}: {e}")
    
    return results

def create_summary_table(results):
    """创建汇总表格"""
    if not results:
        print("没有可用的结果数据")
        return None
    
    df = pd.DataFrame(results)
    
    # 创建汇总表
    summary = []
    for _, row in df.iterrows():
        summary.append({
            '实验类型': row.get('experiment_type', 'unknown'),
            '工作负载': row.get('workload_type', 'unknown'),
            '协议': row.get('protocol', 'unknown'),
            '平均延迟(ms)': round(row.get('average_latency_ms', 0), 2),
            '吞吐量(ops/s)': round(row.get('throughput_ops_per_sec', 0), 2),
            'P99延迟(ms)': round(row.get('p99_latency_ms', 0), 2)
        })
    
    return pd.DataFrame(summary)

def plot_performance_comparison(df, output_dir):
    """绘制性能对比图"""
    if df is None or df.empty:
        print("没有数据可供可视化")
        return
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 1. 延迟对比图
    plt.figure(figsize=(12, 6))
    
    workloads = df['工作负载'].unique()
    protocols = df['协议'].unique()
    
    if len(workloads) > 0 and len(protocols) > 0:
        plt.subplot(1, 2, 1)
        for protocol in protocols:
            protocol_data = df[df['协议'] == protocol]
            if not protocol_data.empty:
                plt.bar(protocol_data['工作负载'], protocol_data['平均延迟(ms)'], 
                       label=protocol, alpha=0.7)
        
        plt.title('平均延迟对比')
        plt.xlabel('工作负载类型')
        plt.ylabel('延迟 (ms)')
        plt.legend()
        plt.xticks(rotation=45)
        
        # 2. 吞吐量对比图
        plt.subplot(1, 2, 2)
        for protocol in protocols:
            protocol_data = df[df['协议'] == protocol]
            if not protocol_data.empty:
                plt.bar(protocol_data['工作负载'], protocol_data['吞吐量(ops/s)'], 
                       label=protocol, alpha=0.7)
        
        plt.title('吞吐量对比')
        plt.xlabel('工作负载类型')
        plt.ylabel('吞吐量 (ops/s)')
        plt.legend()
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.savefig(output_path / 'performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"性能对比图已保存到: {output_path / 'performance_comparison.png'}")

def generate_report(results_dir):
    """生成实验报告"""
    print(f"分析实验结果: {results_dir}")
    
    # 加载结果
    results = load_json_results(results_dir)
    
    if not results:
        print("没有找到有效的实验结果")
        return
    
    print(f"找到 {len(results)} 个有效结果")
    
    # 创建汇总表
    df = create_summary_table(results)
    
    if df is not None:
        print("\n=== 实验结果汇总 ===")
        print(df.to_string(index=False))
        
        # 保存汇总表
        summary_file = Path(results_dir) / "processed_summary.csv"
        df.to_csv(summary_file, index=False)
        print(f"\n汇总表已保存到: {summary_file}")
        
        # 生成可视化
        plot_performance_comparison(df, results_dir)
        
        # 基本分析
        print("\n=== 基本分析 ===")
        
        # 按协议分组统计
        if '协议' in df.columns:
            protocol_stats = df.groupby('协议').agg({
                '平均延迟(ms)': 'mean',
                '吞吐量(ops/s)': 'mean',
                'P99延迟(ms)': 'mean'
            }).round(2)
            
            print("\n各协议平均性能:")
            print(protocol_stats)
        
        # 按工作负载分组统计
        if '工作负载' in df.columns:
            workload_stats = df.groupby('工作负载').agg({
                '平均延迟(ms)': 'mean',
                '吞吐量(ops/s)': 'mean',
                'P99延迟(ms)': 'mean'
            }).round(2)
            
            print("\n各工作负载平均性能:")
            print(workload_stats)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python3 analyze_results.py <results_directory>")
        sys.exit(1)
    
    results_directory = sys.argv[1]
    generate_report(results_directory)
