package experiments

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"math"
	"sort"
	"sync"
	"time"

	"github.com/ailidani/paxi"
)

// MetricsLogger 指标记录器
type MetricsLogger struct {
	mu            sync.RWMutex
	latencies     []time.Duration
	requests      []RequestRecord
	switches      []ProtocolSwitchEvent
	startTime     time.Time
	lastResetTime time.Time
}

// RequestRecord 请求记录
type RequestRecord struct {
	Timestamp time.Time     `json:"timestamp"`
	Command   paxi.Command  `json:"command"`
	Latency   time.Duration `json:"latency"`
	Protocol  string        `json:"protocol"`
	Success   bool          `json:"success"`
	Error     string        `json:"error,omitempty"`
}

// NewMetricsLogger 创建指标记录器
func NewMetricsLogger() *MetricsLogger {
	now := time.Now()
	return &MetricsLogger{
		latencies:     make([]time.Duration, 0, 10000),
		requests:      make([]RequestRecord, 0, 10000),
		switches:      make([]ProtocolSwitchEvent, 0, 100),
		startTime:     now,
		lastResetTime: now,
	}
}

// RecordRequest 记录请求
func (ml *MetricsLogger) RecordRequest(cmd paxi.Command, latency time.Duration, protocol string, success bool, err error) {
	ml.mu.Lock()
	defer ml.mu.Unlock()

	record := RequestRecord{
		Timestamp: time.Now(),
		Command:   cmd,
		Latency:   latency,
		Protocol:  protocol,
		Success:   success,
	}

	if err != nil {
		record.Error = err.Error()
	}

	ml.requests = append(ml.requests, record)
	if success {
		ml.latencies = append(ml.latencies, latency)
	}
}

// RecordProtocolSwitch 记录协议切换
func (ml *MetricsLogger) RecordProtocolSwitch(from, to, reason string, beforeMetrics, afterMetrics PerformanceMetrics, switchCost time.Duration) {
	ml.mu.Lock()
	defer ml.mu.Unlock()

	switchEvent := ProtocolSwitchEvent{
		Timestamp:     time.Now(),
		FromProtocol:  from,
		ToProtocol:    to,
		Reason:        reason,
		BeforeMetrics: beforeMetrics,
		AfterMetrics:  afterMetrics,
		SwitchCost:    float64(switchCost.Milliseconds()),
	}

	ml.switches = append(ml.switches, switchEvent)
}

// GetCurrentMetrics 获取当前指标
func (ml *MetricsLogger) GetCurrentMetrics(protocol string) PerformanceMetrics {
	ml.mu.RLock()
	defer ml.mu.RUnlock()

	now := time.Now()
	duration := now.Sub(ml.lastResetTime).Seconds()
	if duration == 0 {
		duration = 1
	}

	// 过滤最近的请求
	var recentRequests []RequestRecord
	var recentLatencies []time.Duration
	var successCount int64

	cutoff := now.Add(-time.Minute) // 最近一分钟的数据
	for _, req := range ml.requests {
		if req.Timestamp.After(cutoff) {
			recentRequests = append(recentRequests, req)
			if req.Success {
				recentLatencies = append(recentLatencies, req.Latency)
				successCount++
			}
		}
	}

	metrics := PerformanceMetrics{
		Timestamp:    now,
		Protocol:     protocol,
		RequestCount: int64(len(recentRequests)),
		Throughput:   float64(successCount) / duration,
		SuccessRate:  100.0,
	}

	if len(recentRequests) > 0 {
		metrics.SuccessRate = float64(successCount) / float64(len(recentRequests)) * 100
	}

	if len(recentLatencies) > 0 {
		metrics.AverageLatency = calculateAverage(recentLatencies)
		metrics.P50Latency = calculatePercentile(recentLatencies, 0.5)
		metrics.P95Latency = calculatePercentile(recentLatencies, 0.95)
		metrics.P99Latency = calculatePercentile(recentLatencies, 0.99)
		metrics.MinLatency = float64(recentLatencies[0].Milliseconds())
		metrics.MaxLatency = float64(recentLatencies[0].Milliseconds())
		metrics.LatencyStdDev = calculateStdDev(recentLatencies, metrics.AverageLatency)

		// 找到最小和最大延迟
		for _, lat := range recentLatencies {
			latMs := float64(lat.Milliseconds())
			if latMs < metrics.MinLatency {
				metrics.MinLatency = latMs
			}
			if latMs > metrics.MaxLatency {
				metrics.MaxLatency = latMs
			}
		}
	}

	// 统计协议切换次数
	for _, sw := range ml.switches {
		if sw.Timestamp.After(cutoff) {
			metrics.ProtocolSwitches++
		}
	}

	return metrics
}

// Reset 重置指标
func (ml *MetricsLogger) Reset() {
	ml.mu.Lock()
	defer ml.mu.Unlock()

	ml.latencies = ml.latencies[:0]
	ml.requests = ml.requests[:0]
	ml.lastResetTime = time.Now()
}

// SaveToFile 保存指标到文件
func (ml *MetricsLogger) SaveToFile(filename string) error {
	ml.mu.RLock()
	defer ml.mu.RUnlock()

	data := map[string]interface{}{
		"start_time": ml.startTime,
		"end_time":   time.Now(),
		"requests":   ml.requests,
		"switches":   ml.switches,
		"summary":    ml.generateSummary(),
	}

	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化指标数据失败: %v", err)
	}

	return ioutil.WriteFile(filename, jsonData, 0644)
}

// generateSummary 生成摘要统计
func (ml *MetricsLogger) generateSummary() map[string]interface{} {
	totalRequests := len(ml.requests)
	successCount := 0

	for _, req := range ml.requests {
		if req.Success {
			successCount++
		}
	}

	summary := map[string]interface{}{
		"total_requests":      totalRequests,
		"successful_requests": successCount,
		"success_rate":        0.0,
		"total_switches":      len(ml.switches),
		"duration_seconds":    time.Since(ml.startTime).Seconds(),
	}

	if totalRequests > 0 {
		summary["success_rate"] = float64(successCount) / float64(totalRequests) * 100
	}

	if len(ml.latencies) > 0 {
		summary["avg_latency_ms"] = calculateAverage(ml.latencies)
		summary["p95_latency_ms"] = calculatePercentile(ml.latencies, 0.95)
		summary["p99_latency_ms"] = calculatePercentile(ml.latencies, 0.99)
	}

	return summary
}

// 辅助函数
func calculateAverage(latencies []time.Duration) float64 {
	if len(latencies) == 0 {
		return 0
	}

	var sum float64
	for _, lat := range latencies {
		sum += float64(lat.Milliseconds())
	}
	return sum / float64(len(latencies))
}

func calculatePercentile(latencies []time.Duration, percentile float64) float64 {
	if len(latencies) == 0 {
		return 0
	}

	// 转换为毫秒并排序
	values := make([]float64, len(latencies))
	for i, lat := range latencies {
		values[i] = float64(lat.Milliseconds())
	}
	sort.Float64s(values)

	index := int(float64(len(values)-1) * percentile)
	return values[index]
}

func calculateStdDev(latencies []time.Duration, mean float64) float64 {
	if len(latencies) < 2 {
		return 0
	}

	var sum float64
	for _, lat := range latencies {
		diff := float64(lat.Milliseconds()) - mean
		sum += diff * diff
	}
	variance := sum / float64(len(latencies)-1)
	return math.Sqrt(variance)
}
