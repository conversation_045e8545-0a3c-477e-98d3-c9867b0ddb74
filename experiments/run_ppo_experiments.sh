#!/bin/bash

# PPO强化学习协议选择实验脚本
# 该脚本自动运行多种工作负载下的性能对比实验

set -e

# 实验配置
EXPERIMENT_DIR="$(dirname "$0")"
RESULTS_DIR="$EXPERIMENT_DIR/results"
LOGS_DIR="$EXPERIMENT_DIR/logs"
DURATION=300  # 每个实验持续5分钟
WARMUP_TIME=30  # 预热时间30秒

# 创建结果目录
mkdir -p "$RESULTS_DIR"
mkdir -p "$LOGS_DIR"

echo "=== PPO强化学习协议选择实验 ==="
echo "实验持续时间: ${DURATION}秒"
echo "结果保存至: $RESULTS_DIR"

# 工作负载配置
declare -A WORKLOADS=(
    ["mixed_read_write"]="读写混合工作负载"
    ["read_heavy"]="读密集型工作负载"
    ["write_heavy"]="写密集型工作负载"
    ["bursty"]="突发性工作负载"
    ["uniform"]="均匀分布工作负载"
    ["hotspot"]="热点访问工作负载"
)

# 协议配置
PROTOCOLS=("paxos" "epaxos" "m2paxos" "vpaxos")

# 函数：启动服务器集群
start_cluster() {
    local config=$1
    echo "启动服务器集群..."
    
    # 清理之前的进程
    pkill -f "paxi.*server" || true
    sleep 2
    
    # 启动服务器
    for i in {1..3}; do
        nohup ../bin/server -id=1.$i -config="$config" > "$LOGS_DIR/server_$i.log" 2>&1 &
        echo "启动服务器 1.$i"
    done
    
    # 等待服务器启动
    sleep 5
}

# 函数：停止集群
stop_cluster() {
    echo "停止服务器集群..."
    pkill -f "paxi.*server" || true
    sleep 2
}

# 函数：运行基准测试（固定协议）
run_baseline_experiment() {
    local protocol=$1
    local workload=$2
    local output_file=$3
    
    echo "运行基准实验: $protocol + $workload"
    
    # 配置环境变量
    export PROTOCOL_PICKER="fixed"
    export FIXED_PROTOCOL="$protocol"
    export WORKLOAD_TYPE="$workload"
    
    # 启动集群
    start_cluster "../bin/config.json"
    
    # 预热
    echo "预热阶段..."
    timeout $WARMUP_TIME ../bin/benchmark_client -config="../bin/config.json" -workload="$workload" || true
    
    # 正式测试
    echo "正式测试阶段..."
    timeout $DURATION ../bin/benchmark_client -config="../bin/config.json" -workload="$workload" -output="$output_file" || true
    
    stop_cluster
}

# 函数：运行PPO实验
run_ppo_experiment() {
    local workload=$1
    local output_file=$2
    local model_file=$3
    
    echo "运行PPO实验: $workload"
    
    # 配置环境变量
    export PROTOCOL_PICKER="ppo"
    export WORKLOAD_TYPE="$workload"
    export PPO_MODEL_PATH="$model_file"
    export PROTOCOL_SWITCH_INTERVAL_SECONDS=60
    
    # 启动集群
    start_cluster "../bin/config.json"
    
    # 预热（让PPO有时间适应）
    echo "PPO预热和学习阶段..."
    timeout $((WARMUP_TIME * 2)) ../bin/benchmark_client -config="../bin/config.json" -workload="$workload" || true
    
    # 正式测试
    echo "PPO正式测试阶段..."
    timeout $DURATION ../bin/benchmark_client -config="../bin/config.json" -workload="$workload" -output="$output_file" || true
    
    stop_cluster
}

# 函数：生成工作负载配置
generate_workload_config() {
    local workload_type=$1
    local config_file="$RESULTS_DIR/workload_${workload_type}.json"
    
    case $workload_type in
        "mixed_read_write")
            cat > "$config_file" << EOF
{
  "read_ratio": 0.7,
  "write_ratio": 0.3,
  "request_rate": 1000,
  "key_distribution": "uniform",
  "key_range": 10000,
  "value_size": 1024
}
EOF
            ;;
        "read_heavy")
            cat > "$config_file" << EOF
{
  "read_ratio": 0.9,
  "write_ratio": 0.1,
  "request_rate": 1500,
  "key_distribution": "uniform",
  "key_range": 10000,
  "value_size": 1024
}
EOF
            ;;
        "write_heavy")
            cat > "$config_file" << EOF
{
  "read_ratio": 0.2,
  "write_ratio": 0.8,
  "request_rate": 800,
  "key_distribution": "uniform",
  "key_range": 10000,
  "value_size": 1024
}
EOF
            ;;
        "bursty")
            cat > "$config_file" << EOF
{
  "read_ratio": 0.6,
  "write_ratio": 0.4,
  "request_rate": 500,
  "burst_factor": 5,
  "burst_interval": 30,
  "key_distribution": "uniform",
  "key_range": 10000,
  "value_size": 1024
}
EOF
            ;;
        "uniform")
            cat > "$config_file" << EOF
{
  "read_ratio": 0.5,
  "write_ratio": 0.5,
  "request_rate": 1000,
  "key_distribution": "uniform",
  "key_range": 50000,
  "value_size": 1024
}
EOF
            ;;
        "hotspot")
            cat > "$config_file" << EOF
{
  "read_ratio": 0.8,
  "write_ratio": 0.2,
  "request_rate": 1200,
  "key_distribution": "zipfian",
  "zipfian_constant": 0.9,
  "key_range": 10000,
  "value_size": 1024
}
EOF
            ;;
    esac
    
    echo "$config_file"
}

# 主实验循环
main() {
    echo "开始PPO强化学习实验..."
    
    # 创建结果汇总文件
    SUMMARY_FILE="$RESULTS_DIR/experiment_summary.csv"
    echo "实验类型,工作负载,协议,平均延迟(ms),吞吐量(ops/s),P99延迟(ms),标准差" > "$SUMMARY_FILE"
    
    # 对每种工作负载运行实验
    for workload in "${!WORKLOADS[@]}"; do
        echo ""
        echo "=== ${WORKLOADS[$workload]} ==="
        
        # 生成工作负载配置
        workload_config=$(generate_workload_config "$workload")
        
        # 1. 运行基准实验（每种协议单独测试）
        echo "运行基准实验..."
        for protocol in "${PROTOCOLS[@]}"; do
            baseline_output="$RESULTS_DIR/baseline_${workload}_${protocol}.json"
            run_baseline_experiment "$protocol" "$workload" "$baseline_output"
            
            # 解析结果并添加到汇总文件
            if [ -f "$baseline_output" ]; then
                avg_latency=$(jq -r '.average_latency_ms' "$baseline_output" 2>/dev/null || echo "N/A")
                throughput=$(jq -r '.throughput_ops_per_sec' "$baseline_output" 2>/dev/null || echo "N/A")
                p99_latency=$(jq -r '.p99_latency_ms' "$baseline_output" 2>/dev/null || echo "N/A")
                std_dev=$(jq -r '.latency_std_dev_ms' "$baseline_output" 2>/dev/null || echo "N/A")
                
                echo "基准实验,$workload,$protocol,$avg_latency,$throughput,$p99_latency,$std_dev" >> "$SUMMARY_FILE"
            fi
        done
        
        # 2. 运行PPO实验
        echo "运行PPO自适应实验..."
        ppo_output="$RESULTS_DIR/ppo_${workload}.json"
        model_file="$RESULTS_DIR/ppo_model_${workload}.json"
        run_ppo_experiment "$workload" "$ppo_output" "$model_file"
        
        # 解析PPO结果并添加到汇总文件
        if [ -f "$ppo_output" ]; then
            avg_latency=$(jq -r '.average_latency_ms' "$ppo_output" 2>/dev/null || echo "N/A")
            throughput=$(jq -r '.throughput_ops_per_sec' "$ppo_output" 2>/dev/null || echo "N/A")
            p99_latency=$(jq -r '.p99_latency_ms' "$ppo_output" 2>/dev/null || echo "N/A")
            std_dev=$(jq -r '.latency_std_dev_ms' "$ppo_output" 2>/dev/null || echo "N/A")
            
            echo "PPO自适应,$workload,PPO,$avg_latency,$throughput,$p99_latency,$std_dev" >> "$SUMMARY_FILE"
        fi
        
        echo "${WORKLOADS[$workload]}实验完成"
    done
    
    # 生成可视化报告
    echo ""
    echo "生成实验报告..."
    python3 "$EXPERIMENT_DIR/generate_ppo_report.py" "$RESULTS_DIR"
    
    echo ""
    echo "=== 实验完成 ==="
    echo "详细结果请查看: $RESULTS_DIR"
    echo "汇总结果: $SUMMARY_FILE"
}

# 清理函数
cleanup() {
    echo "清理实验环境..."
    stop_cluster
    unset PROTOCOL_PICKER FIXED_PROTOCOL WORKLOAD_TYPE PPO_MODEL_PATH PROTOCOL_SWITCH_INTERVAL_SECONDS
}

# 设置信号处理
trap cleanup EXIT INT TERM

# 运行主函数
main "$@"