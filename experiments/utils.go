package experiments

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
)

// saveJSON 保存JSON数据到文件
func saveJSON(data interface{}, filename string) error {
	jsonData, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化JSON失败: %v", err)
	}

	return ioutil.WriteFile(filename, jsonData, 0644)
}

// loadJSON 从文件加载JSON数据
func loadJSON(filename string, target interface{}) error {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		return fmt.Errorf("读取文件失败: %v", err)
	}

	return json.Unmarshal(data, target)
}

// ensureDir 确保目录存在
func ensureDir(dirPath string) error {
	return os.MkdirAll(dirPath, 0755)
}

// fileExists 检查文件是否存在
func fileExists(filename string) bool {
	_, err := os.Stat(filename)
	return !os.IsNotExist(err)
}

// SaveJSON 导出JSON保存函数
func SaveJSON(data interface{}, filename string) error {
	return saveJSON(data, filename)
}

// LoadJSON 导出JSON加载函数
func LoadJSON(filename string, target interface{}) error {
	return loadJSON(filename, target)
}
