package experiments

import (
	"math"
	"math/rand"
	"sync"
	"time"

	"github.com/ailidani/paxi"
)

// WorkloadGenerator 负载生成器
type WorkloadGenerator struct {
	mu sync.RWMutex
}

// LoadGenerator 具体的负载生成器实例
type LoadGenerator struct {
	pattern     WorkloadPattern
	requestChan chan paxi.Command
	stopChan    chan bool
	rateLimiter *RateLimiter
	keyGen      *KeyGenerator
	running     bool
	mu          sync.RWMutex
}

// RateLimiter 速率限制器
type RateLimiter struct {
	rate     float64
	interval time.Duration
	ticker   *time.Ticker
	tokens   chan struct{}
}

// KeyGenerator 键生成器
type KeyGenerator struct {
	distribution string
	keySpace     int
	zipfGen      *ZipfGenerator
	hotspotKeys  []int
	normalMu     float64
	normalSigma  float64
}

// ZipfGenerator Zipf分布生成器
type ZipfGenerator struct {
	n     uint64
	theta float64
	alpha float64
	zetan float64
	eta   float64
}

// NewWorkloadGenerator 创建负载生成器
func NewWorkloadGenerator() *WorkloadGenerator {
	return &WorkloadGenerator{}
}

// CreateGenerator 创建具体的负载生成器
func (wg *WorkloadGenerator) CreateGenerator(pattern WorkloadPattern) *LoadGenerator {
	generator := &LoadGenerator{
		pattern:     pattern,
		requestChan: make(chan paxi.Command, 1000),
		stopChan:    make(chan bool),
		rateLimiter: NewRateLimiter(pattern.RequestRate),
		keyGen:      NewKeyGenerator(pattern.KeyDistribution, 10000),
	}

	return generator
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(rate float64) *RateLimiter {
	if rate <= 0 {
		rate = 1000 // 默认1000 QPS
	}

	interval := time.Duration(float64(time.Second) / rate)
	limiter := &RateLimiter{
		rate:     rate,
		interval: interval,
		tokens:   make(chan struct{}, int(rate)),
	}

	// 初始化令牌桶
	go limiter.fillTokens()

	return limiter
}

// fillTokens 填充令牌
func (rl *RateLimiter) fillTokens() {
	ticker := time.NewTicker(rl.interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			select {
			case rl.tokens <- struct{}{}:
			default:
				// 令牌桶已满
			}
		}
	}
}

// Wait 等待令牌
func (rl *RateLimiter) Wait() {
	<-rl.tokens
}

// NewKeyGenerator 创建键生成器
func NewKeyGenerator(distribution string, keySpace int) *KeyGenerator {
	kg := &KeyGenerator{
		distribution: distribution,
		keySpace:     keySpace,
		normalMu:     float64(keySpace) / 2,
		normalSigma:  float64(keySpace) / 6,
	}

	switch distribution {
	case "zipfian":
		kg.zipfGen = NewZipfGenerator(uint64(keySpace), 0.99)
	case "hotspot":
		// 创建热点键（20%的键承担80%的负载）
		hotspotCount := keySpace / 5
		kg.hotspotKeys = make([]int, hotspotCount)
		for i := 0; i < hotspotCount; i++ {
			kg.hotspotKeys[i] = rand.Intn(keySpace)
		}
	}

	return kg
}

// GenerateKey 生成键
func (kg *KeyGenerator) GenerateKey() int {
	switch kg.distribution {
	case "uniform":
		return rand.Intn(kg.keySpace)

	case "zipfian":
		if kg.zipfGen != nil {
			return int(kg.zipfGen.Next())
		}
		return rand.Intn(kg.keySpace)

	case "hotspot":
		// 80%概率访问热点键，20%概率访问其他键
		if rand.Float64() < 0.8 && len(kg.hotspotKeys) > 0 {
			return kg.hotspotKeys[rand.Intn(len(kg.hotspotKeys))]
		}
		return rand.Intn(kg.keySpace)

	case "normal":
		key := int(rand.NormFloat64()*kg.normalSigma + kg.normalMu)
		if key < 0 {
			key = 0
		}
		if key >= kg.keySpace {
			key = kg.keySpace - 1
		}
		return key

	default:
		return rand.Intn(kg.keySpace)
	}
}

// NewZipfGenerator 创建Zipf分布生成器
func NewZipfGenerator(n uint64, theta float64) *ZipfGenerator {
	zg := &ZipfGenerator{
		n:     n,
		theta: theta,
		alpha: 1.0 / (1.0 - theta),
	}

	// 计算zetan
	zg.zetan = zg.zetaStatic(n, theta)
	zg.eta = (1.0 - math.Pow(2.0/float64(n), 1.0-theta)) / (1.0 - zg.zetaStatic(2, theta)/zg.zetan)

	return zg
}

// zetaStatic 计算zeta函数
func (zg *ZipfGenerator) zetaStatic(n uint64, theta float64) float64 {
	sum := 0.0
	for i := uint64(1); i <= n; i++ {
		sum += 1.0 / math.Pow(float64(i), theta)
	}
	return sum
}

// Next 生成下一个Zipf分布的值
func (zg *ZipfGenerator) Next() uint64 {
	u := rand.Float64()
	uz := u * zg.zetan

	if uz < 1.0 {
		return 1
	}

	if uz < 1.0+math.Pow(0.5, zg.theta) {
		return 2
	}

	return uint64(float64(zg.n)*math.Pow(zg.eta*u-zg.eta****, zg.alpha)) + 1
}

// Start 启动负载生成器
func (lg *LoadGenerator) Start() {
	lg.mu.Lock()
	if lg.running {
		lg.mu.Unlock()
		return
	}
	lg.running = true
	lg.mu.Unlock()

	go lg.generateLoad()
}

// Stop 停止负载生成器
func (lg *LoadGenerator) Stop() {
	lg.mu.Lock()
	if !lg.running {
		lg.mu.Unlock()
		return
	}
	lg.running = false
	lg.mu.Unlock()

	close(lg.stopChan)
}

// generateLoad 生成负载
func (lg *LoadGenerator) generateLoad() {
	defer close(lg.requestChan)

	for {
		select {
		case <-lg.stopChan:
			return
		default:
			lg.rateLimiter.Wait()

			// 生成请求
			cmd := lg.generateCommand()

			select {
			case lg.requestChan <- cmd:
			case <-lg.stopChan:
				return
			}
		}
	}
}

// generateCommand 生成命令
func (lg *LoadGenerator) generateCommand() paxi.Command {
	key := paxi.Key(lg.keyGen.GenerateKey())

	// 根据读写比例决定操作类型
	isWrite := rand.Float64() < (1.0 / (1.0 + lg.pattern.ReadWriteRatio))

	if isWrite {
		// 生成随机字节数组作为值
		value := make([]byte, 4)
		for i := range value {
			value[i] = byte(rand.Intn(256))
		}
		return paxi.Command{
			Key:   key,
			Value: paxi.Value(value),
		}
	} else {
		return paxi.Command{
			Key:   key,
			Value: nil, // 读操作
		}
	}
}

// GetRequestChannel 获取请求通道
func (lg *LoadGenerator) GetRequestChannel() <-chan paxi.Command {
	return lg.requestChan
}

// UpdatePattern 更新负载模式
func (lg *LoadGenerator) UpdatePattern(pattern WorkloadPattern) {
	lg.mu.Lock()
	defer lg.mu.Unlock()

	lg.pattern = pattern

	// 更新速率限制器
	lg.rateLimiter = NewRateLimiter(pattern.RequestRate)

	// 更新键生成器
	lg.keyGen = NewKeyGenerator(pattern.KeyDistribution, lg.keyGen.keySpace)
}
