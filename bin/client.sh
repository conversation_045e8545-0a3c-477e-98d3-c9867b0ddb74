#!/usr/bin/env bash

PID_FILE=client.pid

PID=$(cat "${PID_FILE}");

if [ -z "${PID}" ]; then
    echo "Process id for servers is written to location: {$PID_FILE}"
    go build ../server/
    go build ../client/
    go build ../cmd/
    ./client -id 1.1 -config ../bin/config.json &
    echo $! >> ${PID_FILE}
    ./client -id 1.2 -config ../bin/config.json &
    echo $! >> ${PID_FILE}
    ./client -id 1.3 -config ../bin/config.json &
    echo $! >> ${PID_FILE}
else
    echo "Client are already started in this folder."
    exit 0
fi